# 大麦网高并发购票系统完整流程详细讲解

## 目录
1. [项目整体架构与技术栈](#1-项目整体架构与技术栈)
2. [系统启动流程详解](#2-系统启动流程详解)
3. [购票前置流程](#3-购票前置流程)
4. [购票核心流程详解](#4-购票核心流程详解)
5. [购票版本对比深度分析](#5-购票版本对比深度分析)
6. [购票后续流程](#6-购票后续流程)
7. [核心组件深度剖析](#7-核心组件深度剖析)
8. [设计模式与架构思想](#8-设计模式与架构思想)
9. [性能优化与监控](#9-性能优化与监控)

## 1. 项目整体架构与技术栈

### 1.1 微服务架构总览

```
┌─────────────────────────────────────────────────────────────────┐
│                        Gateway Service                          │
│                     (网关服务 - 统一入口)                        │
└─────────────────────┬───────────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌─────▼─────┐ ┌──────────┐
│Program Service│ │Order    │ │User       │ │Pay       │
│(节目服务)     │ │Service  │ │Service    │ │Service   │
│- 购票入口     │ │(订单服务)│ │(用户服务) │ │(支付服务)│
│- 座位管理     │ │- 订单创建│ │- 用户认证 │ │- 支付处理│
│- 库存管理     │ │- 订单查询│ │- 购票人   │ │- 退款处理│
└───────────────┘ └─────────┘ └───────────┘ └──────────┘
```

### 1.2 核心技术栈

**基础框架：**
- Spring Boot 2.7.x
- Spring Cloud 2021.x
- MyBatis Plus 3.5.x
- Redis 6.x
- Kafka 2.8.x
- MySQL 8.0.x

**自研框架组件：**
- damai-thread-pool-framework (线程池框架)
- damai-redis-common-framework (Redis缓存框架)
- damai-service-lock-framework (分布式锁框架)
- damai-repeat-execute-limit-framework (防重复执行框架)
- damai-service-delay-queue-framework (延迟队列框架)
- damai-bloom-filter-framework (布隆过滤器框架)

### 1.3 数据存储架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层                                │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   MySQL集群     │   Redis集群     │      Kafka集群              │
│                 │                 │                             │
│ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────────────┐ │
│ │Master-Slave │ │ │ Sentinel    │ │ │ Topic: create_order     │ │
│ │ 读写分离    │ │ │ 高可用      │ │ │ Topic: delay_cancel     │ │
│ │ 分库分表    │ │ │ 主从切换    │ │ │ Topic: api_data         │ │
│ └─────────────┘ │ └─────────────┘ │ └─────────────────────────┘ │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 2. 系统启动流程详解

### 2.1 启动流程总览

系统启动时会执行一系列初始化操作，为高并发购票做准备：

```
系统启动
    ↓
Spring容器初始化
    ↓
自定义组件初始化 ──┬── 组合模式容器初始化
    ↓            ├── 策略模式注册
    ↓            ├── 布隆过滤器初始化
    ↓            ├── 本地锁缓存初始化
    ↓            └── 延迟队列初始化
数据预热
    ↓
服务注册与发现
    ↓
系统就绪
```

### 2.2 组合模式容器初始化详解

**CompositeContainer初始化过程：**

```java
public void init(ConfigurableApplicationContext context) {
    // 1. 获取所有AbstractComposite实现类
    Collection<AbstractComposite> values = context.getBeansOfType(AbstractComposite.class).values();

    // 2. 按type分组
    Map<String, List<AbstractComposite>> typeMap = values.stream()
            .collect(Collectors.groupingBy(AbstractComposite::type));

    // 3. 构建层级树结构
    typeMap.forEach((type, compositeList) -> {
        // 按executeParentOrder分组构建父子关系
        Map<Integer, List<AbstractComposite>> parentOrderMap = compositeList.stream()
                .collect(Collectors.groupingBy(AbstractComposite::executeParentOrder));

        // 构建树形结构
        AbstractComposite rootComposite = buildCompositeTree(parentOrderMap);
        allCompositeInterfaceMap.put(type, rootComposite);
    });
}
```

**初始化的检查器层级结构：**

```
PROGRAM_ORDER_CREATE_CHECK (购票检查)
├── 第1层 (executeParentOrder=0)
│   └── ProgramOrderCreateParamCheckHandler (参数校验)
└── 第2层 (executeParentOrder=1)
    ├── ProgramDetailCheckHandler (节目详情校验)
    └── ProgramUserExistCheckHandler (用户存在性校验)
```

### 2.3 策略模式注册机制

**ProgramOrderStrategy注册过程：**

```java
// 每个策略实现类在启动时自动注册
@Override
public void executeInit(final ConfigurableApplicationContext context) {
    ProgramOrderContext.add(ProgramOrderVersion.V4_VERSION.getVersion(), this);
}
```

**注册后的策略映射：**
```
ProgramOrderContext.MAP:
├── "v1" → ProgramOrderV1Strategy
├── "v2" → ProgramOrderV2Strategy
├── "v3" → ProgramOrderV3Strategy
└── "v4" → ProgramOrderV4Strategy
```

### 2.4 布隆过滤器初始化

**节目ID布隆过滤器预热：**

```java
@PostConstruct
public void init() {
    // 1. 查询所有节目ID
    List<Long> programIds = programMapper.selectAllProgramIds();

    // 2. 添加到布隆过滤器
    for (Long programId : programIds) {
        bloomFilter.put(String.valueOf(programId));
    }

    log.info("布隆过滤器初始化完成，预热节目数量: {}", programIds.size());
}
```

**作用与优势：**
- **快速过滤**：O(1)时间复杂度判断节目是否存在
- **缓存穿透防护**：避免查询不存在的节目ID
- **内存高效**：相比HashSet节省90%以上内存

### 2.5 数据预热机制

**缓存预热流程：**

```java
public boolean programDataPreheat(ProgramDataPreheatDto programDataPreheatDto) {
    // 1. 预热节目基础信息到多级缓存
    ProgramVo programVo = simpleGetProgramAndShowMultipleCache(programDataPreheatDto.getProgramId());

    // 2. 预热座位和库存数据到Redis
    Date showDayTime = programVo.getShowDayTime();
    List<TicketCategoryVo> ticketCategoryVoList = programVo.getTicketCategoryVoList();

    for (TicketCategoryVo ticketCategoryVo : ticketCategoryVoList) {
        // 预热座位数据
        seatService.selectSeatResolution(programDataPreheatDto.getProgramId(),
                ticketCategoryVo.getId(), DateUtils.countBetweenSecond(DateUtils.now(), showDayTime), TimeUnit.SECONDS);

        // 预热余票数量
        ticketCategoryService.getRedisRemainNumberResolution(programDataPreheatDto.getProgramId(), ticketCategoryVo.getId());
    }

    return true;
}
```

**预热的数据结构：**

```
Redis缓存结构:
├── 节目信息: program_info_{programId}
├── 座位信息: seat_no_sold_{programId}_{ticketCategoryId}
├── 锁定座位: seat_lock_{programId}_{ticketCategoryId}
├── 余票数量: ticket_remain_number_{programId}_{ticketCategoryId}
└── 用户购票记录: account_order_count_{userId}_{programId}
```

## 3. 购票前置流程

### 3.1 用户认证与授权流程

```
用户请求 → 网关拦截 → Token验证 → 用户信息获取 → 权限校验 → 请求转发
    ↓           ↓          ↓           ↓           ↓          ↓
  携带Token   JWT解析   Redis查询   用户服务    RBAC校验   目标服务
```

**详细认证流程：**

```java
// 1. 网关Token拦截器
public class AuthenticationFilter implements GlobalFilter {
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String token = getTokenFromRequest(exchange.getRequest());

        // Token验证
        if (!jwtUtil.validateToken(token)) {
            return unauthorized(exchange);
        }

        // 用户信息注入
        Long userId = jwtUtil.getUserIdFromToken(token);
        ServerHttpRequest request = exchange.getRequest().mutate()
                .header("X-User-Id", String.valueOf(userId))
                .build();

        return chain.filter(exchange.mutate().request(request).build());
    }
}
```

### 3.2 节目详情查询流程

**多级缓存查询策略：**

```
查询请求 → 布隆过滤器 → 本地缓存 → Redis缓存 → 数据库 → 缓存回写
    ↓           ↓          ↓          ↓          ↓          ↓
  节目ID     快速过滤    L1缓存     L2缓存     持久化     多级回写
```

**实现代码分析：**

```java
public ProgramVo detailV2(ProgramGetDto programGetDto) {
    // 1. 布隆过滤器快速过滤
    compositeContainer.execute(CompositeCheckType.PROGRAM_DETAIL_CHECK.getValue(), programGetDto);

    // 2. 多级缓存查询
    return simpleGetProgramAndShowMultipleCache(programGetDto.getId());
}

private ProgramVo simpleGetProgramAndShowMultipleCache(Long programId) {
    // 本地缓存 → Redis缓存 → 数据库的查询链路
    return localCacheProgram.get(String.valueOf(programId), () -> {
        return redisCache.get(
            RedisKeyBuild.createRedisKey(RedisKeyManage.PROGRAM_DETAIL, programId),
            ProgramVo.class,
            () -> {
                // 数据库查询逻辑
                return queryFromDatabase(programId);
            },
            tokenExpireManager.getTokenExpireTime(),
            TimeUnit.MINUTES
        );
    });
}
```

### 3.3 购票人信息验证

**购票人信息缓存与验证：**

```java
@Override
protected void execute(ProgramOrderCreateDto programOrderCreateDto) {
    // 1. 从Redis获取购票人列表
    List<TicketUserVo> ticketUserVoList = redisCache.getValueIsList(
        RedisKeyBuild.createRedisKey(RedisKeyManage.TICKET_USER_LIST, programOrderCreateDto.getUserId()),
        TicketUserVo.class);

    // 2. 缓存未命中，RPC调用用户服务
    if (CollectionUtil.isEmpty(ticketUserVoList)) {
        TicketUserListDto ticketUserListDto = new TicketUserListDto();
        ticketUserListDto.setUserId(programOrderCreateDto.getUserId());
        ApiResponse<List<TicketUserVo>> apiResponse = userClient.list(ticketUserListDto);

        if (Objects.equals(apiResponse.getCode(), BaseCode.SUCCESS.getCode())) {
            ticketUserVoList = apiResponse.getData();
            // 缓存购票人信息
            redisCache.set(RedisKeyBuild.createRedisKey(RedisKeyManage.TICKET_USER_LIST, programOrderCreateDto.getUserId()),
                    ticketUserVoList, 30, TimeUnit.MINUTES);
        }
    }

    // 3. 验证购票人ID有效性
    Map<Long, TicketUserVo> ticketUserVoMap = ticketUserVoList.stream()
            .collect(Collectors.toMap(TicketUserVo::getId, ticketUserVo -> ticketUserVo, (v1, v2) -> v2));

    for (Long ticketUserId : programOrderCreateDto.getTicketUserIdList()) {
        if (Objects.isNull(ticketUserVoMap.get(ticketUserId))) {
            throw new DaMaiFrameException(BaseCode.TICKET_USER_EMPTY);
        }
    }
}
```

## 4. 购票核心流程详解

### 4.1 购票V4完整流程架构图

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                购票V4完整流程                                        │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────────┐        │
│  │用户请求 │───▶│ 控制器接收   │───▶│ 策略模式    │───▶│ @RepeatExecute  │        │
│  │         │    │ @Valid校验   │    │ 版本选择    │    │ Limit防重复     │        │
│  └─────────┘    └──────────────┘    └─────────────┘    └─────────────────┘        │
│                                                                ↓                   │
│  ┌─────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────────┐        │
│  │组合检查 │◀───│ 本地锁机制   │◀───│ Redis操作   │◀───│ 组合模式校验    │        │
│  │器执行   │    │ 票档级锁定   │    │ Lua脚本     │    │ 多层级验证      │        │
│  └─────────┘    └──────────────┘    └─────────────┘    └─────────────────┘        │
│       ↓                 ↓                 ↓                                        │
│  ┌─────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────────┐        │
│  │Kafka发送│───▶│ 异步消费     │───▶│ 订单创建    │───▶│ 延迟取消任务    │        │
│  │消息队列 │    │ 延迟检测     │    │ 数据库操作  │    │ Redisson队列    │        │
│  └─────────┘    └──────────────┘    └─────────────┘    └─────────────────┘        │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 购票入口控制器详解

**ProgramOrderController核心实现：**

```java
@RestController
@RequestMapping("/program/order")
@Tag(name = "program-order", description = "节目订单")
public class ProgramOrderController {

    @Operation(summary = "购票V4")
    @PostMapping(value = "/create/v4")
    public ApiResponse<String> createV4(@Valid @RequestBody ProgramOrderCreateDto programOrderCreateDto) {
        return ApiResponse.ok(ProgramOrderContext.get(ProgramOrderVersion.V4_VERSION.getVersion())
                .createOrder(programOrderCreateDto));
    }
}
```

**@Valid注解详细分析：**

`@Valid`注解触发JSR-303验证，对应的DTO验证规则：

```java
@Data
@Schema(title="ProgramOrderCreateDto", description ="节目订单创建")
public class ProgramOrderCreateDto {

    @Schema(name ="programId", type ="Long", description ="节目id", requiredMode= RequiredMode.REQUIRED)
    @NotNull(message = "节目ID不能为空")
    private Long programId;

    @Schema(name ="userId", type ="Long", description ="用户id", requiredMode= RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(name ="ticketUserIdList", type ="List<Long>", description ="购票人id集合", requiredMode= RequiredMode.REQUIRED)
    @NotNull(message = "购票人ID列表不能为空")
    @Size(min = 1, max = 6, message = "购票人数量必须在1-6之间")
    private List<Long> ticketUserIdList;

    @Schema(name ="seatDtoList", type ="List<SeatDto>", description = "座位")
    private List<SeatDto> seatDtoList;

    @Schema(name ="ticketCategoryId", type ="Long", description ="票档id")
    private Long ticketCategoryId;

    @Schema(name ="ticketCount", type ="Integer", description ="票数")
    @Min(value = 1, message = "票数不能小于1")
    @Max(value = 6, message = "票数不能大于6")
    private Integer ticketCount;
}
```

**策略模式上下文获取：**

```java
public static ProgramOrderStrategy get(String version){
    return Optional.ofNullable(MAP.get(version))
            .orElseThrow(() -> new DaMaiFrameException(BaseCode.PROGRAM_ORDER_STRATEGY_NOT_EXIST));
}
```

### 4.3 @RepeatExecuteLimit防重复执行注解深度解析

**注解定义与参数：**

```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RepeatExecuteLimit {

    /**
     * 限制名称，用于区分不同的限制场景
     */
    String name() default "";

    /**
     * 限制的键，支持SpEL表达式
     */
    String[] keys() default {};

    /**
     * 限制时间窗口，默认5秒
     */
    long time() default 5;

    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 错误信息
     */
    String message() default "操作过于频繁，请稍后再试";
}
```

**AOP切面实现原理：**

```java
@Aspect
@Component
public class RepeatExecuteLimitAspect {

    @Autowired
    private RedisCache redisCache;

    @Around("@annotation(repeatExecuteLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RepeatExecuteLimit repeatExecuteLimit) throws Throwable {

        // 1. 构建限制键
        String limitKey = buildLimitKey(joinPoint, repeatExecuteLimit);

        // 2. 检查是否在限制时间内
        if (redisCache.hasKey(RedisKeyBuild.createRedisKey(RedisKeyManage.REPEAT_EXECUTE_LIMIT, limitKey))) {
            throw new DaMaiFrameException(BaseCode.REPEAT_EXECUTE_LIMIT, repeatExecuteLimit.message());
        }

        // 3. 设置限制标记
        redisCache.set(RedisKeyBuild.createRedisKey(RedisKeyManage.REPEAT_EXECUTE_LIMIT, limitKey),
                "1", repeatExecuteLimit.time(), repeatExecuteLimit.timeUnit());

        // 4. 执行目标方法
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            // 5. 异常时删除限制标记，允许重试
            redisCache.del(RedisKeyBuild.createRedisKey(RedisKeyManage.REPEAT_EXECUTE_LIMIT, limitKey));
            throw e;
        }
    }

    private String buildLimitKey(ProceedingJoinPoint joinPoint, RepeatExecuteLimit repeatExecuteLimit) {
        // SpEL表达式解析
        String[] keys = repeatExecuteLimit.keys();
        StringBuilder keyBuilder = new StringBuilder(repeatExecuteLimit.name());

        for (String key : keys) {
            Object value = parseSpEL(key, joinPoint);
            keyBuilder.append(":").append(value);
        }

        return keyBuilder.toString();
    }
}
```

**在购票场景中的应用：**

```java
@RepeatExecuteLimit(
        name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
        keys = {"#programOrderCreateDto.userId","#programOrderCreateDto.programId"},
        time = 5,
        timeUnit = TimeUnit.SECONDS,
        message = "同一用户对同一节目5秒内只能下单一次"
)
```

生成的Redis键格式：`repeat_execute_limit:CREATE_PROGRAM_ORDER:123456:789012`

### 4.4 组合模式校验体系深度剖析

**组合模式执行流程图：**

```
CompositeContainer.execute()
         ↓
    获取校验器树
         ↓
    ┌─────────────────────────────────────┐
    │        第1层校验 (Tier=1)           │
    │  ┌─────────────────────────────────┐ │
    │  │ ProgramOrderCreateParamCheck    │ │
    │  │ - 参数基础校验                  │ │
    │  │ - 购票人ID重复检查              │ │
    │  │ - 座位数量与购票人数量匹配      │ │
    │  └─────────────────────────────────┘ │
    └─────────────────────────────────────┘
         ↓ (executeParentOrder=1)
    ┌─────────────────────────────────────┐
    │        第2层校验 (Tier=2)           │
    │  ┌─────────────────┬───────────────┐ │
    │  │ProgramDetail    │ProgramUser    │ │
    │  │CheckHandler     │ExistCheck     │ │
    │  │- 节目存在性     │Handler        │ │
    │  │- 选座权限       │- 购票人验证   │ │
    │  │- 购买限制       │- 用户权限     │ │
    │  └─────────────────┴───────────────┘ │
    └─────────────────────────────────────┘
```

**AbstractComposite核心实现：**

```java
public abstract class AbstractComposite<T> {

    protected List<AbstractComposite<T>> list = new ArrayList<>();

    /**
     * 层次化执行所有校验器
     */
    public void allExecute(T param) {
        Queue<AbstractComposite<T>> queue = new LinkedList<>();
        queue.add(this);

        while (!queue.isEmpty()) {
            int levelSize = queue.size();

            // 同一层级的校验器并行执行
            for (int i = 0; i < levelSize; i++) {
                AbstractComposite<T> current = queue.poll();

                // 执行当前校验器
                current.execute(param);

                // 添加子校验器到队列
                queue.addAll(current.list);
            }
        }
    }

    /**
     * 具体校验逻辑，由子类实现
     */
    protected abstract void execute(T param);

    /**
     * 校验器类型，用于分组
     */
    public abstract String type();

    /**
     * 父级执行顺序，用于构建层级关系
     */
    public abstract Integer executeParentOrder();

    /**
     * 执行层级
     */
    public abstract Integer executeTier();

    /**
     * 同层级内的执行顺序
     */
    public abstract Integer executeOrder();
}
```

**参数校验器详细实现：**

```java
@Component
public class ProgramOrderCreateParamCheckHandler extends AbstractProgramCheckHandler {

    @Override
    protected void execute(final ProgramOrderCreateDto programOrderCreateDto) {
        List<SeatDto> seatDtoList = programOrderCreateDto.getSeatDtoList();
        List<Long> ticketUserIdList = programOrderCreateDto.getTicketUserIdList();

        // 1. 检查购票人ID是否重复
        Map<Long, List<Long>> ticketUserIdMap = ticketUserIdList.stream()
                .collect(Collectors.groupingBy(ticketUserId -> ticketUserId));

        for (List<Long> value : ticketUserIdMap.values()) {
            if (value.size() > 1) {
                throw new DaMaiFrameException(BaseCode.TICKET_USER_ID_REPEAT);
            }
        }

        // 2. 选座模式校验
        if (CollectionUtil.isNotEmpty(seatDtoList)) {
            // 座位数量与购票人数量必须匹配
            if (seatDtoList.size() != ticketUserIdList.size()) {
                throw new DaMaiFrameException(BaseCode.TICKET_USER_COUNT_UNEQUAL_SEAT_COUNT);
            }

            // 座位信息完整性校验
            for (SeatDto seatDto : seatDtoList) {
                validateSeatDto(seatDto);
            }
        } else {
            // 非选座模式校验
            validateNonSeatMode(programOrderCreateDto);
        }
    }

    private void validateSeatDto(SeatDto seatDto) {
        if (Objects.isNull(seatDto.getId())) {
            throw new DaMaiFrameException(BaseCode.SEAT_ID_EMPTY);
        }
        if (Objects.isNull(seatDto.getTicketCategoryId())) {
            throw new DaMaiFrameException(BaseCode.SEAT_TICKET_CATEGORY_ID_EMPTY);
        }
        if (Objects.isNull(seatDto.getRowCode())) {
            throw new DaMaiFrameException(BaseCode.SEAT_ROW_CODE_EMPTY);
        }
        if (Objects.isNull(seatDto.getColCode())) {
            throw new DaMaiFrameException(BaseCode.SEAT_COL_CODE_EMPTY);
        }
        if (Objects.isNull(seatDto.getPrice())) {
            throw new DaMaiFrameException(BaseCode.SEAT_PRICE_EMPTY);
        }
    }

    private void validateNonSeatMode(ProgramOrderCreateDto programOrderCreateDto) {
        if (Objects.isNull(programOrderCreateDto.getTicketCategoryId())) {
            throw new DaMaiFrameException(BaseCode.TICKET_CATEGORY_NOT_EXIST);
        }
        if (Objects.isNull(programOrderCreateDto.getTicketCount())) {
            throw new DaMaiFrameException(BaseCode.TICKET_COUNT_NOT_EXIST);
        }
        if (programOrderCreateDto.getTicketCount() <= 0) {
            throw new DaMaiFrameException(BaseCode.TICKET_COUNT_ERROR);
        }
    }

    @Override
    public Integer executeParentOrder() { return 0; }  // 根节点

    @Override
    public Integer executeTier() { return 1; }         // 第1层

    @Override
    public Integer executeOrder() { return 1; }        // 第1个执行
}
```

### 4.5 本地锁机制深度解析

**BaseProgramOrder本地锁实现：**

```java
@Component
public class BaseProgramOrder {

    @Autowired
    private LocalLockCache localLockCache;

    public String localLockCreateOrder(String lockKeyPrefix,
                                      ProgramOrderCreateDto programOrderCreateDto,
                                      LockTask<String> lockTask) {

        // 1. 提取需要锁定的票档ID列表
        List<Long> ticketCategoryIdList = extractTicketCategoryIds(programOrderCreateDto);

        // 2. 为每个票档创建锁对象
        List<ReentrantLock> localLockList = createLocks(lockKeyPrefix, programOrderCreateDto, ticketCategoryIdList);

        // 3. 按顺序获取锁（避免死锁）
        List<ReentrantLock> localLockSuccessList = acquireLocks(localLockList);

        try {
            // 4. 执行业务逻辑
            return lockTask.execute();
        } finally {
            // 5. 逆序释放锁
            releaseLocks(localLockSuccessList);
        }
    }

    private List<Long> extractTicketCategoryIds(ProgramOrderCreateDto programOrderCreateDto) {
        List<SeatDto> seatDtoList = programOrderCreateDto.getSeatDtoList();
        List<Long> ticketCategoryIdList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(seatDtoList)) {
            // 选座模式：从座位信息中提取票档ID
            ticketCategoryIdList = seatDtoList.stream()
                    .map(SeatDto::getTicketCategoryId)
                    .distinct()
                    .sorted()  // 排序确保锁获取顺序一致
                    .collect(Collectors.toList());
        } else {
            // 非选座模式：直接使用票档ID
            ticketCategoryIdList.add(programOrderCreateDto.getTicketCategoryId());
        }

        return ticketCategoryIdList;
    }

    private List<ReentrantLock> createLocks(String lockKeyPrefix,
                                           ProgramOrderCreateDto programOrderCreateDto,
                                           List<Long> ticketCategoryIdList) {
        List<ReentrantLock> localLockList = new ArrayList<>(ticketCategoryIdList.size());

        for (Long ticketCategoryId : ticketCategoryIdList) {
            // 锁键格式：PROGRAM_ORDER_CREATE_V4-{programId}-{ticketCategoryId}
            String lockKey = StrUtil.join("-", lockKeyPrefix,
                    programOrderCreateDto.getProgramId(), ticketCategoryId);

            ReentrantLock localLock = localLockCache.getLock(lockKey, false);
            localLockList.add(localLock);
        }

        return localLockList;
    }

    private List<ReentrantLock> acquireLocks(List<ReentrantLock> localLockList) {
        List<ReentrantLock> localLockSuccessList = new ArrayList<>(localLockList.size());

        for (ReentrantLock reentrantLock : localLockList) {
            try {
                // 尝试获取锁，超时时间5秒
                boolean acquired = reentrantLock.tryLock(5, TimeUnit.SECONDS);
                if (!acquired) {
                    // 获取锁失败，释放已获取的锁
                    releaseLocks(localLockSuccessList);
                    throw new DaMaiFrameException(BaseCode.ACQUIRE_LOCK_TIMEOUT);
                }
                localLockSuccessList.add(reentrantLock);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                releaseLocks(localLockSuccessList);
                throw new DaMaiFrameException(BaseCode.ACQUIRE_LOCK_INTERRUPTED);
            }
        }

        return localLockSuccessList;
    }

    private void releaseLocks(List<ReentrantLock> localLockSuccessList) {
        // 逆序释放锁，避免死锁
        for (int i = localLockSuccessList.size() - 1; i >= 0; i--) {
            ReentrantLock reentrantLock = localLockSuccessList.get(i);
            try {
                if (reentrantLock.isHeldByCurrentThread()) {
                    reentrantLock.unlock();
                }
            } catch (Throwable t) {
                log.error("local lock unlock error", t);
            }
        }
    }
}
```

**LocalLockCache实现原理：**

```java
@Component
public class LocalLockCache {

    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    public ReentrantLock getLock(String lockKey, boolean fair) {
        return lockMap.computeIfAbsent(lockKey, key -> new ReentrantLock(fair));
    }

    public void removeLock(String lockKey) {
        ReentrantLock lock = lockMap.get(lockKey);
        if (lock != null && !lock.hasQueuedThreads() && !lock.isLocked()) {
            lockMap.remove(lockKey);
        }
    }
}
```

**本地锁设计优势：**

1. **细粒度锁定**：按票档ID分别加锁，不同票档可并行处理
2. **避免死锁**：按票档ID排序后获取锁，保证获取顺序一致
3. **超时机制**：tryLock避免无限等待
4. **异常安全**：finally块确保锁一定被释放
5. **性能优化**：本地锁比分布式锁性能更好

### 4.6 Redis缓存操作与Lua脚本深度解析

**Redis缓存数据结构设计：**

```
Redis Key设计规范:
├── 座位相关
│   ├── seat_no_sold_{programId}_{ticketCategoryId}     # 未售出座位Hash
│   ├── seat_lock_{programId}_{ticketCategoryId}        # 锁定座位Hash
│   └── seat_sold_{programId}_{ticketCategoryId}        # 已售出座位Hash
├── 库存相关
│   └── ticket_remain_number_{programId}_{ticketCategoryId}  # 余票数量Hash
├── 订单相关
│   ├── account_order_count_{userId}_{programId}        # 用户购票数量
│   └── order_mq_{orderNumber}                          # 订单MQ标记
└── 业务记录
    └── program_record_{programId}                      # 购票记录Hash
```

**createOrderOperateProgramCacheResolution核心流程：**

```java
public CreateOrderTemporaryData createOrderOperateProgramCacheResolution(ProgramOrderCreateDto programOrderCreateDto){

    // 1. 从多级缓存中查找节目演出时间
    ProgramShowTime programShowTime = programShowTimeService
            .selectProgramShowTimeByProgramIdMultipleCache(programOrderCreateDto.getProgramId());

    // 2. 查询对应的票档类型
    List<TicketCategoryVo> getTicketCategoryList = getTicketCategoryList(programOrderCreateDto, programShowTime.getShowTime());

    // 3. 预热缓存数据
    for (TicketCategoryVo ticketCategory : getTicketCategoryList) {
        // 预热座位数据到Redis
        seatService.selectSeatResolution(programOrderCreateDto.getProgramId(),
                ticketCategory.getId(),
                DateUtils.countBetweenSecond(DateUtils.now(), programShowTime.getShowTime()),
                TimeUnit.SECONDS);

        // 预热余票数量到Redis
        ticketCategoryService.getRedisRemainNumberResolution(programOrderCreateDto.getProgramId(),
                ticketCategory.getId());
    }

    // 4. 构建Lua脚本参数
    String identifierId = String.valueOf(uidGenerator.getUid());
    List<String> keys = buildLuaScriptKeys(programOrderCreateDto, identifierId);
    String[] data = buildLuaScriptData(programOrderCreateDto);

    // 5. 执行Lua脚本进行原子性操作
    ProgramCacheCreateOrderData programCacheCreateOrderData =
            programCacheCreateOrderResolutionOperate.programCacheOperate(keys, data);

    // 6. 检查执行结果
    if (!Objects.equals(programCacheCreateOrderData.getCode(), BaseCode.SUCCESS.getCode())) {
        throw new DaMaiFrameException(Objects.requireNonNull(BaseCode.getRc(programCacheCreateOrderData.getCode())));
    }

    return new CreateOrderTemporaryData(identifierId, programCacheCreateOrderData.getPurchaseSeatList());
}
```

**Lua脚本完整实现分析：**

```lua
-- programDataCreateOrderResolution.lua
-- 购票核心Lua脚本，保证Redis操作原子性

-- ==================== 参数定义 ====================
local type = tonumber(KEYS[1])                    -- 类型：1=选座 2=自动匹配
local placeholder_seat_no_sold_hash_key = KEYS[2] -- 未售座位key模板
local placeholder_seat_lock_hash_key = KEYS[3]    -- 锁定座位key模板
local program_id = KEYS[4]                        -- 节目ID
local record_hash_key = KEYS[5]                   -- 记录key模板
local identifier_id = KEYS[6]                     -- 操作标识ID
local record_type = KEYS[7]                       -- 记录类型

-- 解析JSON参数
local ticket_count_list = cjson.decode(ARGV[1])   -- 票档数量列表
local ticket_user_id_list = cjson.decode(ARGV[3]) -- 购票人ID列表

-- ==================== 变量初始化 ====================
local purchase_seat_list = {}                     -- 购买座位列表
local total_seat_dto_price = 0                    -- 入参座位总价
local total_seat_vo_price = 0                     -- 缓存座位总价
local ticket_category_record_list = {}            -- 票档记录列表
local seat_id_list = {}                           -- 座位ID列表（按票档分组）
local seat_data_list = {}                         -- 座位数据列表（按票档分组）
local lock_status = 2                             -- 锁定状态

-- ==================== 选座模式处理 ====================
if (type == 1) then
    -- 1. 验证票档余量
    for index, ticket_count in ipairs(ticket_count_list) do
        local ticket_remain_number_hash_key = ticket_count.programTicketRemainNumberHashKey
        local ticket_category_id = ticket_count.ticketCategoryId
        local count = ticket_count.ticketCount

        -- 检查余票数量
        local remain_number_str = redis.call('hget', ticket_remain_number_hash_key, tostring(ticket_category_id))
        if not remain_number_str then
            return string.format('{"%s": %d}', 'code', 40010)  -- 票档不存在
        end

        local remain_number = tonumber(remain_number_str)
        if (count > remain_number) then
            return string.format('{"%s": %d}', 'code', 40011)  -- 余票不足
        end

        -- 构建票档记录
        local ticket_category_record = {
            ticketCategoryId = ticket_category_id,
            ticketCount = count,
            beforeRemainNumber = remain_number,
            afterRemainNumber = remain_number - count
        }
        table.insert(ticket_category_record_list, ticket_category_record)
    end

    -- 2. 验证座位可用性
    local seat_data_list_param = cjson.decode(ARGV[2])
    local seat_index = 0

    for index, seatData in pairs(seat_data_list_param) do
        local seat_no_sold_hash_key = seatData.seatNoSoldHashKey
        local seat_dto_list = cjson.decode(seatData.seatDataList)

        for index2, seat_dto in ipairs(seat_dto_list) do
            seat_index = seat_index + 1
            local id = seat_dto.id
            local seat_dto_price = seat_dto.price

            -- 从Redis获取座位信息
            local seat_vo_str = redis.call('hget', seat_no_sold_hash_key, tostring(id))
            if not seat_vo_str then
                return string.format('{"%s": %d}', 'code', 40001)  -- 座位不存在
            end

            local seat_vo = cjson.decode(seat_vo_str)

            -- 检查座位状态
            if (seat_vo.sellStatus == 2) then
                return string.format('{"%s": %d}', 'code', 40002)  -- 座位已锁定
            end

            if (seat_vo.sellStatus == 1) then
                return string.format('{"%s": %d}', 'code', 40003)  -- 座位已售出
            end

            -- 价格校验
            if (seat_dto_price ~= seat_vo.price) then
                return string.format('{"%s": %d}', 'code', 40004)  -- 价格不匹配
            end

            -- 累计价格
            total_seat_dto_price = total_seat_dto_price + seat_dto_price
            total_seat_vo_price = total_seat_vo_price + seat_vo.price

            -- 构建购买座位信息
            local purchase_seat = {
                id = seat_vo.id,
                programId = seat_vo.programId,
                ticketCategoryId = seat_vo.ticketCategoryId,
                rowCode = seat_vo.rowCode,
                colCode = seat_vo.colCode,
                price = seat_vo.price,
                ticketUserId = ticket_user_id_list[seat_index]
            }
            table.insert(purchase_seat_list, purchase_seat)

            -- 准备座位状态更新数据
            prepareSeatUpdate(seat_vo, seat_vo.ticketCategoryId, seat_id_list, seat_data_list, lock_status)
        end
    end

    -- 3. 价格总和校验
    if (total_seat_dto_price ~= total_seat_vo_price) then
        return string.format('{"%s": %d}', 'code', 40005)  -- 总价不匹配
    end

-- ==================== 自动匹配模式处理 ====================
else
    -- 自动匹配座位逻辑（类似选座模式，但自动选择座位）
    for index, ticket_count in ipairs(ticket_count_list) do
        local ticket_remain_number_hash_key = ticket_count.programTicketRemainNumberHashKey
        local ticket_category_id = ticket_count.ticketCategoryId
        local count = ticket_count.ticketCount

        -- 检查余票数量
        local remain_number_str = redis.call('hget', ticket_remain_number_hash_key, tostring(ticket_category_id))
        if not remain_number_str then
            return string.format('{"%s": %d}', 'code', 40010)
        end

        local remain_number = tonumber(remain_number_str)
        if (count > remain_number) then
            return string.format('{"%s": %d}', 'code', 40011)
        end

        -- 自动选择座位
        local selected_seats = autoSelectSeats(program_id, ticket_category_id, count)
        if (#selected_seats < count) then
            return string.format('{"%s": %d}', 'code', 40012)  -- 可用座位不足
        end

        -- 处理选中的座位
        for i, seat in ipairs(selected_seats) do
            local purchase_seat = {
                id = seat.id,
                programId = seat.programId,
                ticketCategoryId = seat.ticketCategoryId,
                rowCode = seat.rowCode,
                colCode = seat.colCode,
                price = seat.price,
                ticketUserId = ticket_user_id_list[i]
            }
            table.insert(purchase_seat_list, purchase_seat)

            prepareSeatUpdate(seat, ticket_category_id, seat_id_list, seat_data_list, lock_status)
        end
    end
end

-- ==================== 原子性更新操作 ====================

-- 1. 扣减余票数量
for index, ticket_count in ipairs(ticket_count_list) do
    local ticket_remain_number_hash_key = ticket_count.programTicketRemainNumberHashKey
    local ticket_category_id = ticket_count.ticketCategoryId
    local count = ticket_count.ticketCount

    redis.call('hincrby', ticket_remain_number_hash_key, ticket_category_id, "-" .. count)
end

-- 2. 移动座位状态：未售出 → 锁定
for ticket_category_id, seat_id_array in pairs(seat_id_list) do
    redis.call('hdel',
        string.format(placeholder_seat_no_sold_hash_key, program_id, tostring(ticket_category_id)),
        unpack(seat_id_array))
end

for ticket_category_id, seat_data_array in pairs(seat_data_list) do
    redis.call('hmset',
        string.format(placeholder_seat_lock_hash_key, program_id, tostring(ticket_category_id)),
        unpack(seat_data_array))
end

-- 3. 记录操作流水
local time = redis.call("time")
local currentTimeMillis = (time[1] * 1000) + math.floor(time[2] / 1000)

local purchase_record = {
    recordType = record_type,
    timestamp = currentTimeMillis,
    ticketCategoryRecordList = ticket_category_record_list
}

redis.call('hset',
    string.format(record_hash_key, program_id),
    identifier_id,
    cjson.encode(purchase_record))

-- ==================== 返回结果 ====================
return string.format('{"%s": %d, "%s": %s}',
    'code', 0,
    'purchaseSeatList', cjson.encode(purchase_seat_list))

-- ==================== 辅助函数 ====================
function prepareSeatUpdate(seat, ticket_category_id, seat_id_list, seat_data_list, lock_status)
    if not seat_id_list[ticket_category_id] then
        seat_id_list[ticket_category_id] = {}
    end
    if not seat_data_list[ticket_category_id] then
        seat_data_list[ticket_category_id] = {}
    end

    -- 添加座位ID
    table.insert(seat_id_list[ticket_category_id], tostring(seat.id))

    -- 更新座位状态并添加座位数据
    seat.sellStatus = lock_status
    table.insert(seat_data_list[ticket_category_id], tostring(seat.id))
    table.insert(seat_data_list[ticket_category_id], cjson.encode(seat))
end

function autoSelectSeats(program_id, ticket_category_id, count)
    local seat_no_sold_hash_key = string.format(placeholder_seat_no_sold_hash_key, program_id, tostring(ticket_category_id))
    local seat_keys = redis.call('hkeys', seat_no_sold_hash_key)

    local selected_seats = {}
    for i = 1, math.min(count, #seat_keys) do
        local seat_data = redis.call('hget', seat_no_sold_hash_key, seat_keys[i])
        if seat_data then
            local seat = cjson.decode(seat_data)
            if seat.sellStatus == 0 then  -- 未售出状态
                table.insert(selected_seats, seat)
            end
        end
    end

    return selected_seats
end
```

**Lua脚本执行优势分析：**

1. **原子性保证**：所有Redis操作在单个Lua脚本中执行，要么全部成功，要么全部失败
2. **性能优化**：减少网络往返次数，从多次Redis调用减少到一次
3. **数据一致性**：避免并发情况下的数据不一致问题
4. **复杂业务逻辑**：支持复杂的座位选择和库存扣减逻辑
5. **错误处理**：详细的错误码返回，便于业务层处理

### 4.7 Kafka异步消息处理深度解析

**异步处理架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            Kafka异步处理架构                                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │Program      │───▶│Kafka        │───▶│Order        │───▶│Database     │      │
│  │Service      │    │Producer     │    │Service      │    │Transaction  │      │
│  │             │    │             │    │Consumer     │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │订单数据     │    │消息队列     │    │延迟检测     │    │订单创建     │      │
│  │封装构建     │    │异步解耦     │    │超时丢弃     │    │状态更新     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**doCreateV2异步处理实现：**

```java
private String doCreateV2(ProgramOrderCreateDto programOrderCreateDto,
                          CreateOrderTemporaryData createOrderTemporaryData,
                          Integer orderVersion) {

    // 1. 构建订单创建参数
    OrderCreateDto orderCreateDto = buildCreateOrderParamV2(
            programOrderCreateDto.getProgramId(),
            programOrderCreateDto.getUserId(),
            createOrderTemporaryData.getPurchaseSeatList(),
            orderVersion);

    // 2. 封装Kafka消息对象
    OrderCreateMq orderCreateMq = new OrderCreateMq();
    BeanUtils.copyProperties(orderCreateDto, orderCreateMq);
    orderCreateMq.setIdentifierId(createOrderTemporaryData.getIdentifierId());
    orderCreateMq.setCreateOrderTime(new Date());  // 设置创建时间用于延迟检测

    // 3. 异步创建节目记录任务
    BusinessThreadPool.execute(() -> createProgramRecordTask(orderCreateMq.getProgramId()));

    // 4. 通过Kafka异步创建订单
    String orderNumber = createOrderByMq(orderCreateMq, createOrderTemporaryData.getPurchaseSeatList());

    // 5. 设置延迟取消任务
    DelayOrderCancelDto delayOrderCancelDto = new DelayOrderCancelDto();
    delayOrderCancelDto.setOrderNumber(orderCreateDto.getOrderNumber());
    delayOrderCancelSend.sendMessage(JSON.toJSONString(delayOrderCancelDto));

    return orderNumber;
}
```

**createOrderByMq详细实现：**

```java
private String createOrderByMq(OrderCreateMq orderCreateMq, List<PurchaseSeat> purchaseSeatList) {
    CreateOrderMqDomain createOrderMqDomain = new CreateOrderMqDomain();
    CountDownLatch latch = new CountDownLatch(1);

    // 发送Kafka消息
    createOrderSend.sendMessage(JSON.toJSONString(orderCreateMq),
        // 成功回调
        sendResult -> {
            createOrderMqDomain.orderNumber = String.valueOf(orderCreateMq.getOrderNumber());
            log.info("创建订单kafka发送消息成功 topic : {}", sendResult.getRecordMetadata().topic());
            latch.countDown();
        },
        // 失败回调
        ex -> {
            log.error("创建订单kafka发送消息失败 error", ex);

            // Kafka发送失败，回滚Redis操作
            List<SeatVo> purchaseSeatVoList = purchaseSeatList.stream().map(purchaseSeat -> {
                SeatVo seatVo = new SeatVo();
                BeanUtils.copyProperties(purchaseSeat, seatVo);
                return seatVo;
            }).collect(Collectors.toList());

            // 回滚座位和库存
            updateProgramCacheDataResolution(orderCreateMq.getProgramId(), purchaseSeatVoList, OrderStatus.CANCEL);

            createOrderMqDomain.daMaiFrameException = new DaMaiFrameException(BaseCode.KAFKA_SEND_ERROR);
            latch.countDown();
        });

    try {
        // 等待Kafka发送结果，最多等待5秒
        boolean await = latch.await(5, TimeUnit.SECONDS);
        if (!await) {
            throw new DaMaiFrameException(BaseCode.KAFKA_SEND_TIMEOUT);
        }

        if (Objects.nonNull(createOrderMqDomain.daMaiFrameException)) {
            throw createOrderMqDomain.daMaiFrameException;
        }

        return createOrderMqDomain.orderNumber;

    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        throw new DaMaiFrameException(BaseCode.KAFKA_SEND_INTERRUPTED);
    }
}
```

**CreateOrderSend Kafka生产者：**

```java
@Component
public class CreateOrderSend {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private KafkaTopic kafkaTopic;

    public void sendMessage(String message,
                           SuccessCallback<SendResult<String, String>> successCallback,
                           FailureCallback failureCallback) {

        log.info("创建订单kafka发送消息 消息体 : {}", message);

        // 构建Topic名称：环境前缀 + 配置的topic
        String topicName = SpringUtil.getPrefixDistinctionName() + "-" + kafkaTopic.getTopic();

        // 异步发送消息
        CompletableFuture<SendResult<String, String>> completableFuture =
                kafkaTemplate.send(topicName, message);

        // 设置回调
        completableFuture.whenComplete((result, ex) -> {
            if (Objects.isNull(ex)) {
                successCallback.onSuccess(result);
            } else {
                failureCallback.onFailure(ex);
            }
        });
    }
}
```

### 4.8 订单服务Kafka消费处理

**CreateOrderConsumer消费者实现：**

```java
@Component
public class CreateOrderConsumer {

    private static final long MESSAGE_DELAY_TIME = 3000; // 3秒延迟阈值

    @Autowired
    private OrderService orderService;

    @Autowired
    private RedisCache redisCache;

    @KafkaListener(topics = {SPRING_INJECT_PREFIX_DISTINCTION_NAME+"-"+"${spring.kafka.topic:create_order}"})
    public void consumerOrderMessage(ConsumerRecord<String,String> consumerRecord) {
        try {
            Optional.ofNullable(consumerRecord.value()).map(String::valueOf).ifPresent(value -> {

                // 1. 解析消息
                OrderCreateMq orderCreateMq = JSON.parseObject(value, OrderCreateMq.class);

                // 2. 计算消息延迟时间
                long createOrderTimeTimestamp = orderCreateMq.getCreateOrderTime().getTime();
                long currentTimeTimestamp = System.currentTimeMillis();
                long delayTime = currentTimeTimestamp - createOrderTimeTimestamp;

                log.info("消费到kafka的创建订单消息 消息体: {} 延迟时间 : {} 毫秒", value, delayTime);

                // 3. 延迟检测与处理
                if (delayTime > MESSAGE_DELAY_TIME) {
                    handleDelayedMessage(orderCreateMq, delayTime);
                } else {
                    // 4. 正常处理订单创建
                    String orderNumber = orderService.createByMq(orderCreateMq);
                    log.info("消费到kafka的创建订单消息 创建订单成功 订单号 : {}", orderNumber);
                }
            });
        } catch (Exception e) {
            log.error("处理消费到kafka的创建订单消息失败 error", e);
        }
    }

    private void handleDelayedMessage(OrderCreateMq orderCreateMq, long delayTime) {
        // 构建座位信息映射
        Map<Long, List<OrderTicketUserCreateDto>> orderTicketUserSeatList = orderCreateMq
                .getOrderTicketUserCreateDtoList().stream()
                .collect(Collectors.groupingBy(OrderTicketUserCreateDto::getTicketCategoryId));

        // 构建座位ID映射（用于日志记录）
        Map<Long, List<Long>> seatMap = new HashMap<>(orderTicketUserSeatList.size());
        orderTicketUserSeatList.forEach((k, v) -> {
            seatMap.put(k, v.stream()
                    .map(OrderTicketUserCreateDto::getSeatId)
                    .collect(Collectors.toList()));
        });

        log.info("消费到kafka的创建订单消息延迟时间大于了 {} 毫秒 此订单消息被丢弃 订单号 : {} 座位信息 : {}",
                delayTime, orderCreateMq.getOrderNumber(), JSON.toJSONString(seatMap));

        // 将延迟丢弃的订单放入Redis中，用于后续分析和补偿
        redisCache.leftPushForList(
                RedisKeyBuild.createRedisKey(RedisKeyManage.DISCARD_ORDER, orderCreateMq.getProgramId()),
                new DiscardOrder(orderCreateMq, DiscardOrderReason.CONSUMER_DELAY.getCode()));
    }
}
```

**延迟消息处理策略：**

1. **延迟阈值设定**：3秒作为消息处理延迟的阈值
2. **丢弃策略**：超过阈值的消息直接丢弃，避免处理过期订单
3. **补偿记录**：丢弃的订单记录到Redis，便于后续分析和补偿
4. **座位回滚**：丢弃订单时，座位状态已在Redis中锁定，需要通过补偿机制回滚

## 5. 购票版本对比深度分析

### 5.1 四个版本技术架构对比图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            购票版本技术架构对比                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  V1版本：分布式锁 + 同步处理                                                    │
│  ┌─────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │
│  │请求接收 │─▶│Redisson     │─▶│同步订单创建 │─▶│数据库事务   │                │
│  │         │  │分布式锁     │  │             │  │             │                │
│  └─────────┘  └─────────────┘  └─────────────┘  └─────────────┘                │
│                                                                                 │
│  V2版本：分布式锁 + 本地锁 + 同步处理                                           │
│  ┌─────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │
│  │请求接收 │─▶│双重锁机制   │─▶│同步订单创建 │─▶│数据库事务   │                │
│  │         │  │Redisson+    │  │             │  │             │                │
│  │         │  │ReentrantLock│  │             │  │             │                │
│  └─────────┘  └─────────────┘  └─────────────┘  └─────────────┘                │
│                                                                                 │
│  V3版本：本地锁 + 同步处理                                                      │
│  ┌─────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │
│  │请求接收 │─▶│ReentrantLock│─▶│同步订单创建 │─▶│数据库事务   │                │
│  │         │  │本地锁       │  │             │  │             │                │
│  └─────────┘  └─────────────┘  └─────────────┘  └─────────────┘                │
│                                                                                 │
│  V4版本：本地锁 + 异步处理（推荐）                                              │
│  ┌─────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │
│  │请求接收 │─▶│ReentrantLock│─▶│Redis+Kafka  │─▶│异步订单创建 │                │
│  │         │  │本地锁       │  │异步处理     │  │             │                │
│  └─────────┘  └─────────────┘  └─────────────┘  └─────────────┘                │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 5.2 V1版本：分布式锁同步处理详解

**ProgramOrderV1Strategy实现：**

```java
@Component
public class ProgramOrderV1Strategy extends AbstractApplicationCommandLineRunnerHandler
        implements ProgramOrderStrategy {

    @Autowired
    private ProgramOrderService programOrderService;

    @Autowired
    private CompositeContainer compositeContainer;

    @RepeatExecuteLimit(
            name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
            keys = {"#programOrderCreateDto.userId","#programOrderCreateDto.programId"})
    @ServiceLock(name = PROGRAM_ORDER_CREATE_V1, keys = {"#programOrderCreateDto.programId"})
    @Override
    public String createOrder(final ProgramOrderCreateDto programOrderCreateDto) {
        // 1. 组合校验
        compositeContainer.execute(CompositeCheckType.PROGRAM_ORDER_CREATE_CHECK.getValue(), programOrderCreateDto);

        // 2. 同步创建订单
        return programOrderService.create(programOrderCreateDto, ProgramOrderVersion.V1_VERSION.getValue());
    }

    @Override
    public Integer executeOrder() { return 1; }

    @Override
    public void executeInit(final ConfigurableApplicationContext context) {
        ProgramOrderContext.add(ProgramOrderVersion.V1_VERSION.getVersion(), this);
    }
}
```

**@ServiceLock分布式锁注解详解：**

```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ServiceLock {

    /**
     * 锁名称
     */
    String name() default "";

    /**
     * 锁的键，支持SpEL表达式
     */
    String[] keys() default {};

    /**
     * 锁类型：读锁/写锁
     */
    LockType lockType() default LockType.Write;

    /**
     * 等待时间
     */
    long waitTime() default 3;

    /**
     * 持有时间
     */
    long leaseTime() default 10;

    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 是否公平锁
     */
    boolean fair() default false;
}
```

**ServiceLock AOP实现：**

```java
@Aspect
@Component
public class ServiceLockAspect {

    @Autowired
    private ServiceLockTool serviceLockTool;

    @Around("@annotation(serviceLock)")
    public Object around(ProceedingJoinPoint joinPoint, ServiceLock serviceLock) throws Throwable {

        // 1. 构建锁键
        String lockKey = buildLockKey(joinPoint, serviceLock);

        // 2. 获取分布式锁
        RLock lock = serviceLockTool.getLock(lockKey, serviceLock.lockType(), serviceLock.fair());

        // 3. 尝试获取锁
        boolean acquired = false;
        try {
            acquired = lock.tryLock(serviceLock.waitTime(), serviceLock.leaseTime(), serviceLock.timeUnit());

            if (!acquired) {
                throw new DaMaiFrameException(BaseCode.ACQUIRE_DISTRIBUTED_LOCK_TIMEOUT);
            }

            // 4. 执行业务逻辑
            return joinPoint.proceed();

        } finally {
            // 5. 释放锁
            if (acquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

**V1版本特点分析：**

**优势：**
- **强一致性**：分布式锁保证跨节点的数据一致性
- **简单可靠**：逻辑简单，容易理解和维护
- **事务完整性**：同步处理保证事务的完整性

**劣势：**
- **性能瓶颈**：分布式锁成为性能瓶颈
- **锁竞争激烈**：按节目ID加锁，粒度较粗
- **响应时间长**：同步处理导致用户等待时间长
- **可扩展性差**：难以应对高并发场景

### 5.3 V2版本：双重锁机制详解

**ProgramOrderV2Strategy实现：**

```java
@Component
public class ProgramOrderV2Strategy extends AbstractApplicationCommandLineRunnerHandler
        implements ProgramOrderStrategy {

    @Autowired
    private ProgramOrderService programOrderService;

    @Autowired
    private CompositeContainer compositeContainer;

    @Autowired
    private ServiceLockTool serviceLockTool;

    @Autowired
    private LocalLockCache localLockCache;

    @RepeatExecuteLimit(
            name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
            keys = {"#programOrderCreateDto.userId","#programOrderCreateDto.programId"})
    @Override
    public String createOrder(ProgramOrderCreateDto programOrderCreateDto) {
        // 1. 组合校验
        compositeContainer.execute(CompositeCheckType.PROGRAM_ORDER_CREATE_CHECK.getValue(), programOrderCreateDto);

        // 2. 提取票档ID列表
        List<SeatDto> seatDtoList = programOrderCreateDto.getSeatDtoList();
        List<Long> ticketCategoryIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(seatDtoList)) {
            ticketCategoryIdList = seatDtoList.stream()
                    .map(SeatDto::getTicketCategoryId)
                    .distinct()
                    .sorted()  // 排序避免死锁
                    .collect(Collectors.toList());
        } else {
            ticketCategoryIdList.add(programOrderCreateDto.getTicketCategoryId());
        }

        // 3. 双重锁机制
        List<RLock> serviceLockList = new ArrayList<>(ticketCategoryIdList.size());
        List<RLock> serviceLockSuccessList = new ArrayList<>(ticketCategoryIdList.size());
        List<ReentrantLock> localLockList = new ArrayList<>(ticketCategoryIdList.size());
        List<ReentrantLock> localLockSuccessList = new ArrayList<>(ticketCategoryIdList.size());

        // 4. 创建锁对象
        for (Long ticketCategoryId : ticketCategoryIdList) {
            // 分布式锁
            String serviceLockKey = StrUtil.join("-", PROGRAM_ORDER_CREATE_V2,
                    programOrderCreateDto.getProgramId(), ticketCategoryId);
            RLock serviceLock = serviceLockTool.getLock(serviceLockKey, LockType.Write, false);
            serviceLockList.add(serviceLock);

            // 本地锁
            String localLockKey = StrUtil.join("-", PROGRAM_ORDER_CREATE_V2_LOCAL,
                    programOrderCreateDto.getProgramId(), ticketCategoryId);
            ReentrantLock localLock = localLockCache.getLock(localLockKey, false);
            localLockList.add(localLock);
        }

        // 5. 按顺序获取分布式锁
        for (RLock rLock : serviceLockList) {
            try {
                boolean acquired = rLock.tryLock(3, 10, TimeUnit.SECONDS);
                if (!acquired) {
                    throw new DaMaiFrameException(BaseCode.ACQUIRE_DISTRIBUTED_LOCK_TIMEOUT);
                }
                serviceLockSuccessList.add(rLock);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new DaMaiFrameException(BaseCode.ACQUIRE_DISTRIBUTED_LOCK_INTERRUPTED);
            }
        }

        // 6. 按顺序获取本地锁
        for (ReentrantLock reentrantLock : localLockList) {
            try {
                boolean acquired = reentrantLock.tryLock(3, TimeUnit.SECONDS);
                if (!acquired) {
                    throw new DaMaiFrameException(BaseCode.ACQUIRE_LOCAL_LOCK_TIMEOUT);
                }
                localLockSuccessList.add(reentrantLock);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new DaMaiFrameException(BaseCode.ACQUIRE_LOCAL_LOCK_INTERRUPTED);
            }
        }

        try {
            // 7. 执行业务逻辑
            return programOrderService.create(programOrderCreateDto, ProgramOrderVersion.V2_VERSION.getValue());
        } finally {
            // 8. 逆序释放锁
            releaseLocks(serviceLockSuccessList, localLockSuccessList);
        }
    }

    private void releaseLocks(List<RLock> serviceLockSuccessList, List<ReentrantLock> localLockSuccessList) {
        // 先释放分布式锁
        for (int i = serviceLockSuccessList.size() - 1; i >= 0; i--) {
            RLock rLock = serviceLockSuccessList.get(i);
            try {
                if (rLock.isHeldByCurrentThread()) {
                    rLock.unlock();
                }
            } catch (Throwable t) {
                log.error("service lock unlock error", t);
            }
        }

        // 再释放本地锁
        for (int i = localLockSuccessList.size() - 1; i >= 0; i--) {
            ReentrantLock reentrantLock = localLockSuccessList.get(i);
            try {
                if (reentrantLock.isHeldByCurrentThread()) {
                    reentrantLock.unlock();
                }
            } catch (Throwable t) {
                log.error("local lock unlock error", t);
            }
        }
    }
}
```

**V2版本双重锁机制分析：**

**锁获取顺序：**
1. 先获取分布式锁（跨节点保护）
2. 再获取本地锁（节点内保护）

**锁释放顺序：**
1. 先释放分布式锁
2. 再释放本地锁

**优势：**
- **更细粒度**：按票档ID分别加锁
- **双重保护**：分布式锁 + 本地锁双重保护
- **减少竞争**：不同票档可以并行处理

**劣势：**
- **复杂度高**：锁管理逻辑复杂
- **性能提升有限**：仍然是同步处理
- **死锁风险**：多个锁增加死锁风险

### 5.4 V3版本：纯本地锁同步处理详解

**ProgramOrderV3Strategy实现：**

```java
@Component
public class ProgramOrderV3Strategy extends AbstractApplicationCommandLineRunnerHandler
        implements ProgramOrderStrategy {

    @Autowired
    private ProgramOrderService programOrderService;

    @Autowired
    private CompositeContainer compositeContainer;

    @Autowired
    private BaseProgramOrder baseProgramOrder;

    @RepeatExecuteLimit(
            name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
            keys = {"#programOrderCreateDto.userId","#programOrderCreateDto.programId"})
    @Override
    public String createOrder(ProgramOrderCreateDto programOrderCreateDto) {
        // 1. 组合校验
        compositeContainer.execute(CompositeCheckType.PROGRAM_ORDER_CREATE_CHECK.getValue(), programOrderCreateDto);

        // 2. 本地锁 + 同步处理
        return baseProgramOrder.localLockCreateOrder(PROGRAM_ORDER_CREATE_V3, programOrderCreateDto,
                () -> programOrderService.createNew(programOrderCreateDto, ProgramOrderVersion.V3_VERSION.getValue()));
    }

    @Override
    public Integer executeOrder() { return 3; }

    @Override
    public void executeInit(final ConfigurableApplicationContext context) {
        ProgramOrderContext.add(ProgramOrderVersion.V3_VERSION.getVersion(), this);
    }
}
```

**createNew同步处理方法：**

```java
public String createNew(ProgramOrderCreateDto programOrderCreateDto, Integer orderVersion) {
    // 1. 操作Redis缓存（通过Lua脚本）
    CreateOrderTemporaryData createOrderTemporaryData = createOrderOperateProgramCacheResolution(programOrderCreateDto);

    // 2. 转换座位数据
    List<SeatVo> purchaseSeatList = createOrderTemporaryData.getPurchaseSeatList().stream().map(purchaseSeat -> {
        SeatVo seatVo = new SeatVo();
        BeanUtils.copyProperties(purchaseSeat, seatVo);
        return seatVo;
    }).collect(Collectors.toList());

    // 3. 同步创建订单
    return doCreate(programOrderCreateDto, purchaseSeatList, orderVersion);
}
```

**V3版本特点分析：**

**优势：**
- **性能更好**：去除分布式锁，减少网络开销
- **逻辑简化**：只使用本地锁，逻辑相对简单
- **响应更快**：本地锁获取速度快

**劣势：**
- **分布式一致性弱**：在分布式环境下可能出现超卖
- **适用场景有限**：只适合单节点或读写分离场景
- **数据不一致风险**：多节点并发时可能出现数据不一致

### 5.5 V4版本：本地锁异步处理详解（推荐方案）

**ProgramOrderV4Strategy完整实现：**

```java
@Component
public class ProgramOrderV4Strategy extends AbstractApplicationCommandLineRunnerHandler
        implements ProgramOrderStrategy {

    @Autowired
    private ProgramOrderService programOrderService;

    @Autowired
    private CompositeContainer compositeContainer;

    @Autowired
    private BaseProgramOrder baseProgramOrder;

    @RepeatExecuteLimit(
            name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
            keys = {"#programOrderCreateDto.userId","#programOrderCreateDto.programId"})
    @Override
    public String createOrder(ProgramOrderCreateDto programOrderCreateDto) {
        // 1. 组合校验
        compositeContainer.execute(CompositeCheckType.PROGRAM_ORDER_CREATE_CHECK.getValue(), programOrderCreateDto);

        // 2. 本地锁 + 异步处理
        return baseProgramOrder.localLockCreateOrder(PROGRAM_ORDER_CREATE_V4, programOrderCreateDto,
                () -> programOrderService.createNewAsync(programOrderCreateDto, ProgramOrderVersion.V4_VERSION.getValue()));
    }

    @Override
    public Integer executeOrder() { return 4; }

    @Override
    public void executeInit(final ConfigurableApplicationContext context) {
        ProgramOrderContext.add(ProgramOrderVersion.V4_VERSION.getVersion(), this);
    }
}
```

**V4版本核心优势：**

1. **高并发处理能力**：
   - 本地锁减少网络开销
   - 异步处理提高吞吐量
   - Kafka削峰填谷

2. **最终一致性保证**：
   - Redis原子操作保证缓存一致性
   - 补偿机制处理异常情况
   - 延迟队列处理超时订单

3. **性能优化**：
   - 用户快速得到响应
   - 后台异步处理订单
   - 减少数据库压力

4. **容错能力强**：
   - Kafka消息持久化
   - 延迟检测机制
   - 丢弃订单记录与补偿

**版本选择建议：**

| 场景 | 推荐版本 | 原因 |
|------|----------|------|
| 低并发场景 | V1 | 简单可靠，强一致性 |
| 中等并发场景 | V3 | 性能较好，逻辑简单 |
| 高并发场景 | V4 | 高性能，高可用，最终一致性 |
| 分布式强一致性要求 | V1/V2 | 分布式锁保证强一致性 |
| 性能优先场景 | V4 | 异步处理，高吞吐量 |

## 6. 购票后续流程

### 6.1 订单创建后续处理流程图

```
订单创建成功
    ↓
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            订单后续处理流程                                     │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │延迟取消任务 │    │订单支付     │    │支付回调     │    │订单完成     │      │
│  │Redisson     │───▶│支付宝/微信  │───▶│异步通知     │───▶│状态更新     │      │
│  │DelayQueue   │    │             │    │             │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │15分钟后     │    │支付状态查询 │    │订单状态同步 │    │座位状态更新 │      │
│  │自动取消     │    │定时轮询     │    │Redis缓存    │    │已售出       │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 6.2 延迟取消任务详解

> [!caution]
>
> 注意V4版本未使用延迟
>
> 1. **数据一致性策略不同**
>
> **V1/V2/V3版本（同步创建订单）：**
>
> - 订单创建是同步的，支付成功后需要更新座位状态
> - 使用**延迟消息**是为了解耦订单服务和节目服务
> - 避免在订单支付流程中直接调用节目服务，减少服务间耦合
>
> **V4版本（异步创建订单）：**
>
> - 订单创建本身就是异步的，已经通过Kafka解耦
> - 支付成功后需要**立即更新**座位状态，保证数据实时一致性
> - 因为V4版本的座位状态在下单时就已经锁定，支付后需要立即变更为已售出
>
> 2. **座位状态管理机制不同**
>
> **V1/V2/V3版本的座位状态流转：**
>
> 未售出 → 下单时锁定 → 支付后通过延迟消息变为已售出
>
> **V4版本的座位状态流转：**
>
> ![image-20250608000038032](img/image-20250608000038032.png)
>
> 3. **性能考虑**
>
> **V1/V2/V3版本：**
>
> - 同步处理性能较低，使用延迟消息避免阻塞支付流程
> - 延迟更新可以批量处理，提高吞吐量
>
> **V4版本：**
>
> - 异步处理性能较高，可以承受立即更新的开销
> - 实时更新保证用户看到的座位状态是最新的
>
> 延迟消息的作用
>
> `delayOperateProgramDataSend.sendMessage()` 的作用是：
>
> 1. **解耦服务调用**：避免订单服务直接依赖节目服务
> 2. **异步处理**：不阻塞支付成功的响应
> 3. **重试机制**：消息队列提供重试保障
> 4. **批量优化**：可以批量处理座位状态更新
>
> ## 总结
>
> 这种设计体现了**渐进式优化**的思想：
>
> - **V1/V2/V3**：采用传统的延迟更新机制，保证系统稳定性
> - **V4**：采用更先进的实时更新机制，提供更好的用户体验
>
> V4版本不需要延迟消息是因为它的整体架构已经是异步的，可以承受实时更新的性能开销，同时提供更好的数据一致性保证。

**DelayOrderCancelSend延迟任务发送：**

```java
@Component
public class DelayOrderCancelSend {

    @Autowired
    private DelayQueueContext delayQueueContext;

    public void sendMessage(String message) {
        try {
            log.info("延迟订单取消消息进行发送 消息体 : {}", message);

            // 发送到Redisson延迟队列，15分钟后执行
            delayQueueContext.sendMessage(
                SpringUtil.getPrefixDistinctionName() + "-" + DELAY_ORDER_CANCEL_TOPIC,
                message,
                DELAY_ORDER_CANCEL_TIME,      // 15分钟
                DELAY_ORDER_CANCEL_TIME_UNIT  // TimeUnit.MINUTES
            );
        } catch (Exception e) {
            log.error("send delay message error message : {}", message, e);
        }
    }
}
```

**DelayQueueContext延迟队列上下文：**

```java
@Component
public class DelayQueueContext {

    private final Map<String, DelayProduceQueue> delayProduceQueueMap = new ConcurrentHashMap<>();
    private final Map<String, DelayConsumerQueue> delayConsumerQueueMap = new ConcurrentHashMap<>();

    @Autowired
    private RedissonClient redissonClient;

    public void sendMessage(String topic, String message, long delayTime, TimeUnit timeUnit) {
        DelayProduceQueue delayProduceQueue = delayProduceQueueMap.computeIfAbsent(topic,
            key -> new DelayProduceQueue(redissonClient, key));

        delayProduceQueue.offer(message, delayTime, timeUnit);
    }

    public void registerConsumer(String topic, ConsumerTask consumerTask) {
        DelayConsumerQueue delayConsumerQueue = delayConsumerQueueMap.computeIfAbsent(topic,
            key -> new DelayConsumerQueue(createDelayQueuePart(), key));

        delayConsumerQueue.setConsumerTask(consumerTask);
        delayConsumerQueue.listenStart();
    }
}
```

**DelayOrderCancelConsumer延迟取消消费者：**

```java
@Component
public class DelayOrderCancelConsumer implements ConsumerTask {

    @Autowired
    private OrderService orderService;

    @Override
    public void execute(String content) {
        log.info("延迟订单取消消息进行消费 content : {}", content);

        if (StringUtil.isEmpty(content)) {
            log.error("延迟队列消息不存在");
            return;
        }

        try {
            // 1. 解析延迟取消消息
            DelayOrderCancelDto delayOrderCancelDto = JSON.parseObject(content, DelayOrderCancelDto.class);

            // 2. 执行订单取消逻辑
            OrderCancelDto orderCancelDto = new OrderCancelDto();
            orderCancelDto.setOrderNumber(delayOrderCancelDto.getOrderNumber());

            boolean cancelResult = orderService.cancel(orderCancelDto);

            if (cancelResult) {
                log.info("延迟订单取消成功 orderNumber : {}", delayOrderCancelDto.getOrderNumber());
            } else {
                log.warn("延迟订单取消失败，订单可能已支付 orderNumber : {}", delayOrderCancelDto.getOrderNumber());
            }

        } catch (Exception e) {
            log.error("延迟订单取消处理异常 content : {}", content, e);
        }
    }

    @Override
    public String topic() {
        return SpringUtil.getPrefixDistinctionName() + "-" + DELAY_ORDER_CANCEL_TOPIC;
    }
}
```

### 6.3 订单取消逻辑详解

**OrderService.cancel方法实现：**

```java
@Transactional(rollbackFor = Exception.class)
public boolean cancel(OrderCancelDto orderCancelDto) {

    // 1. 查询订单信息
    LambdaQueryWrapper<Order> orderLambdaQueryWrapper = Wrappers.lambdaQuery(Order.class)
            .eq(Order::getOrderNumber, orderCancelDto.getOrderNumber());
    Order order = orderMapper.selectOne(orderLambdaQueryWrapper);

    if (Objects.isNull(order)) {
        log.warn("订单不存在，取消失败 orderNumber : {}", orderCancelDto.getOrderNumber());
        return false;
    }

    // 2. 检查订单状态
    if (!canCancelOrder(order)) {
        log.warn("订单状态不允许取消 orderNumber : {} status : {}",
                orderCancelDto.getOrderNumber(), order.getStatus());
        return false;
    }

    // 3. 更新订单状态为已取消
    Order updateOrder = new Order();
    updateOrder.setId(order.getId());
    updateOrder.setStatus(OrderStatus.CANCEL.getCode());
    updateOrder.setCancelTime(DateUtils.now());
    updateOrder.setEditTime(DateUtils.now());

    int updateResult = orderMapper.updateById(updateOrder);
    if (updateResult <= 0) {
        throw new DaMaiFrameException(BaseCode.ORDER_CANCEL_UPDATE_FAIL);
    }

    // 4. 回滚座位和库存
    rollbackSeatAndStock(order);

    // 5. 更新用户购票数量缓存
    updateUserOrderCount(order);

    log.info("订单取消成功 orderNumber : {}", orderCancelDto.getOrderNumber());
    return true;
}

private boolean canCancelOrder(Order order) {
    // 只有未支付状态的订单才能取消
    return OrderStatus.NO_PAY.getCode().equals(order.getStatus());
}

private void rollbackSeatAndStock(Order order) {
    try {
        // 1. 查询订单座位信息
        List<OrderTicketUser> orderTicketUserList = orderTicketUserService.selectByOrderNumber(order.getOrderNumber());

        if (CollectionUtil.isEmpty(orderTicketUserList)) {
            log.warn("订单座位信息为空，无需回滚 orderNumber : {}", order.getOrderNumber());
            return;
        }

        // 2. 构建座位回滚数据
        List<SeatVo> rollbackSeatList = orderTicketUserList.stream().map(orderTicketUser -> {
            SeatVo seatVo = new SeatVo();
            seatVo.setId(orderTicketUser.getSeatId());
            seatVo.setProgramId(order.getProgramId());
            seatVo.setTicketCategoryId(orderTicketUser.getTicketCategoryId());
            seatVo.setRowCode(orderTicketUser.getRowCode());
            seatVo.setColCode(orderTicketUser.getColCode());
            seatVo.setPrice(orderTicketUser.getPrice());
            return seatVo;
        }).collect(Collectors.toList());

        // 3. 调用节目服务回滚缓存数据
        ProgramCacheUpdateDto programCacheUpdateDto = new ProgramCacheUpdateDto();
        programCacheUpdateDto.setProgramId(order.getProgramId());
        programCacheUpdateDto.setSeatVoList(rollbackSeatList);
        programCacheUpdateDto.setOrderStatus(OrderStatus.CANCEL);

        ApiResponse<Boolean> rollbackResponse = programClient.updateProgramCacheData(programCacheUpdateDto);

        if (!Objects.equals(rollbackResponse.getCode(), BaseCode.SUCCESS.getCode())) {
            log.error("座位和库存回滚失败 orderNumber : {} response : {}",
                    order.getOrderNumber(), JSON.toJSONString(rollbackResponse));
            throw new DaMaiFrameException(BaseCode.SEAT_STOCK_ROLLBACK_FAIL);
        }

        log.info("座位和库存回滚成功 orderNumber : {} seatCount : {}",
                order.getOrderNumber(), rollbackSeatList.size());

    } catch (Exception e) {
        log.error("座位和库存回滚异常 orderNumber : {}", order.getOrderNumber(), e);
        throw new DaMaiFrameException(BaseCode.SEAT_STOCK_ROLLBACK_ERROR);
    }
}

private void updateUserOrderCount(Order order) {
    try {
        // 查询订单购票数量
        int ticketCount = orderTicketUserService.countByOrderNumber(order.getOrderNumber());

        // 减少用户购票数量缓存
        String cacheKey = RedisKeyBuild.createRedisKey(RedisKeyManage.ACCOUNT_ORDER_COUNT,
                order.getUserId(), order.getProgramId()).getRelKey();

        redisCache.decrBy(cacheKey, ticketCount);

        log.info("用户购票数量缓存更新成功 userId : {} programId : {} decreaseCount : {}",
                order.getUserId(), order.getProgramId(), ticketCount);

    } catch (Exception e) {
        log.error("用户购票数量缓存更新失败 orderNumber : {}", order.getOrderNumber(), e);
        // 缓存更新失败不影响订单取消，只记录日志
    }
}
```

### 6.4 支付流程详解

**订单支付接口：**

```java
@Operation(summary = "订单支付")
@PostMapping(value = "/pay")
public ApiResponse<String> pay(@Valid @RequestBody OrderPayDto orderPayDto) {
    return ApiResponse.ok(orderService.pay(orderPayDto));
}
```

**OrderService.pay方法实现：**

```java
@Transactional(rollbackFor = Exception.class)
public String pay(OrderPayDto orderPayDto) {

    // 1. 查询订单信息
    LambdaQueryWrapper<Order> orderLambdaQueryWrapper = Wrappers.lambdaQuery(Order.class)
            .eq(Order::getOrderNumber, orderPayDto.getOrderNumber());
    Order order = orderMapper.selectOne(orderLambdaQueryWrapper);

    if (Objects.isNull(order)) {
        throw new DaMaiFrameException(BaseCode.ORDER_NOT_EXIST);
    }

    // 2. 检查订单状态
    if (!OrderStatus.NO_PAY.getCode().equals(order.getStatus())) {
        throw new DaMaiFrameException(BaseCode.ORDER_STATUS_NOT_ALLOW_PAY);
    }

    // 3. 检查订单是否超时
    if (isOrderTimeout(order)) {
        // 超时订单自动取消
        OrderCancelDto orderCancelDto = new OrderCancelDto();
        orderCancelDto.setOrderNumber(order.getOrderNumber());
        cancel(orderCancelDto);
        throw new DaMaiFrameException(BaseCode.ORDER_TIMEOUT_CANCELLED);
    }

    // 4. 构建支付参数
    PayCreateDto payCreateDto = buildPayCreateDto(order, orderPayDto);

    // 5. 调用支付服务
    ApiResponse<String> payResponse = payClient.create(payCreateDto);

    if (!Objects.equals(payResponse.getCode(), BaseCode.SUCCESS.getCode())) {
        log.error("支付服务调用失败 orderNumber : {} response : {}",
                order.getOrderNumber(), JSON.toJSONString(payResponse));
        throw new DaMaiFrameException(payResponse);
    }

    // 6. 更新订单状态为支付中
    updateOrderStatusToPaying(order);

    log.info("订单支付请求成功 orderNumber : {} payUrl : {}",
            order.getOrderNumber(), payResponse.getData());

    return payResponse.getData();
}

private boolean isOrderTimeout(Order order) {
    // 检查订单是否超过15分钟未支付
    long createTime = order.getCreateTime().getTime();
    long currentTime = System.currentTimeMillis();
    long timeoutMillis = 15 * 60 * 1000; // 15分钟

    return (currentTime - createTime) > timeoutMillis;
}

private PayCreateDto buildPayCreateDto(Order order, OrderPayDto orderPayDto) {
    PayCreateDto payCreateDto = new PayCreateDto();
    payCreateDto.setOutTradeNo(String.valueOf(order.getOrderNumber()));
    payCreateDto.setTotalAmount(order.getOrderPrice());
    payCreateDto.setSubject(order.getProgramTitle());
    payCreateDto.setBody("大麦网购票-" + order.getProgramTitle());
    payCreateDto.setPayType(orderPayDto.getPayType());
    payCreateDto.setUserId(order.getUserId());

    return payCreateDto;
}

private void updateOrderStatusToPaying(Order order) {
    Order updateOrder = new Order();
    updateOrder.setId(order.getId());
    updateOrder.setStatus(OrderStatus.PAYING.getCode());
    updateOrder.setEditTime(DateUtils.now());

    int updateResult = orderMapper.updateById(updateOrder);
    if (updateResult <= 0) {
        throw new DaMaiFrameException(BaseCode.ORDER_STATUS_UPDATE_FAIL);
    }
}
```

### 6.5 支付回调处理

**支付宝回调接口：**

```java
@Operation(summary = "支付宝支付后回调通知")
@PostMapping(value = "/alipay/notify")
public String alipayNotify(HttpServletRequest request) {
    return orderService.alipayNotify(request);
}
```

**alipayNotify回调处理：**

```java
public String alipayNotify(HttpServletRequest request) {
    try {
        // 1. 获取支付宝回调参数
        Map<String, String> params = getAlipayNotifyParams(request);

        // 2. 验证签名
        boolean signVerified = AlipaySignature.rsaCheckV1(params,
                alipayConfig.getAlipayPublicKey(),
                alipayConfig.getCharset(),
                alipayConfig.getSignType());

        if (!signVerified) {
            log.error("支付宝回调签名验证失败 params : {}", JSON.toJSONString(params));
            return "failure";
        }

        // 3. 解析回调参数
        String outTradeNo = params.get("out_trade_no");    // 订单号
        String tradeStatus = params.get("trade_status");   // 交易状态
        String totalAmount = params.get("total_amount");   // 支付金额
        String tradeNo = params.get("trade_no");           // 支付宝交易号

        // 4. 处理支付成功回调
        if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
            boolean processResult = processPaymentSuccess(outTradeNo, tradeNo, totalAmount);
            return processResult ? "success" : "failure";
        }

        log.info("支付宝回调状态非成功 outTradeNo : {} tradeStatus : {}", outTradeNo, tradeStatus);
        return "success";

    } catch (Exception e) {
        log.error("支付宝回调处理异常", e);
        return "failure";
    }
}

@Transactional(rollbackFor = Exception.class)
private boolean processPaymentSuccess(String outTradeNo, String tradeNo, String totalAmount) {

    // 1. 查询订单
    Long orderNumber = Long.valueOf(outTradeNo);
    LambdaQueryWrapper<Order> orderLambdaQueryWrapper = Wrappers.lambdaQuery(Order.class)
            .eq(Order::getOrderNumber, orderNumber);
    Order order = orderMapper.selectOne(orderLambdaQueryWrapper);

    if (Objects.isNull(order)) {
        log.error("支付回调订单不存在 orderNumber : {}", orderNumber);
        return false;
    }

    // 2. 检查订单状态
    if (OrderStatus.PAID.getCode().equals(order.getStatus())) {
        log.info("订单已支付，忽略重复回调 orderNumber : {}", orderNumber);
        return true;
    }

    if (!OrderStatus.PAYING.getCode().equals(order.getStatus())) {
        log.error("订单状态不正确，无法处理支付回调 orderNumber : {} status : {}",
                orderNumber, order.getStatus());
        return false;
    }

    // 3. 验证支付金额
    if (!order.getOrderPrice().equals(new BigDecimal(totalAmount))) {
        log.error("支付金额不匹配 orderNumber : {} orderPrice : {} payAmount : {}",
                orderNumber, order.getOrderPrice(), totalAmount);
        return false;
    }

    // 4. 更新订单状态为已支付
    Order updateOrder = new Order();
    updateOrder.setId(order.getId());
    updateOrder.setStatus(OrderStatus.PAID.getCode());
    updateOrder.setPayTime(DateUtils.now());
    updateOrder.setPayTradeNo(tradeNo);
    updateOrder.setEditTime(DateUtils.now());

    int updateResult = orderMapper.updateById(updateOrder);
    if (updateResult <= 0) {
        log.error("订单状态更新失败 orderNumber : {}", orderNumber);
        return false;
    }

    // 5. 更新座位状态为已售出
    updateSeatStatusToSold(order);

    // 6. 发送支付成功通知
    sendPaymentSuccessNotification(order);

    log.info("订单支付成功处理完成 orderNumber : {} tradeNo : {}", orderNumber, tradeNo);
    return true;
}

private void updateSeatStatusToSold(Order order) {
    try {
        // 1. 查询订单座位信息
        List<OrderTicketUser> orderTicketUserList = orderTicketUserService.selectByOrderNumber(order.getOrderNumber());

        // 2. 构建座位更新数据
        List<SeatVo> seatVoList = orderTicketUserList.stream().map(orderTicketUser -> {
            SeatVo seatVo = new SeatVo();
            seatVo.setId(orderTicketUser.getSeatId());
            seatVo.setProgramId(order.getProgramId());
            seatVo.setTicketCategoryId(orderTicketUser.getTicketCategoryId());
            seatVo.setRowCode(orderTicketUser.getRowCode());
            seatVo.setColCode(orderTicketUser.getColCode());
            seatVo.setPrice(orderTicketUser.getPrice());
            return seatVo;
        }).collect(Collectors.toList());

        // 3. 调用节目服务更新座位状态
        ProgramCacheUpdateDto programCacheUpdateDto = new ProgramCacheUpdateDto();
        programCacheUpdateDto.setProgramId(order.getProgramId());
        programCacheUpdateDto.setSeatVoList(seatVoList);
        programCacheUpdateDto.setOrderStatus(OrderStatus.PAID);

        ApiResponse<Boolean> updateResponse = programClient.updateProgramCacheData(programCacheUpdateDto);

        if (!Objects.equals(updateResponse.getCode(), BaseCode.SUCCESS.getCode())) {
            log.error("座位状态更新失败 orderNumber : {} response : {}",
                    order.getOrderNumber(), JSON.toJSONString(updateResponse));
        } else {
            log.info("座位状态更新成功 orderNumber : {} seatCount : {}",
                    order.getOrderNumber(), seatVoList.size());
        }

    } catch (Exception e) {
        log.error("座位状态更新异常 orderNumber : {}", order.getOrderNumber(), e);
    }
}
```

### 6.6 对账与数据一致性保证

**对账任务执行接口：**

```java
@Operation(summary = "对账任务执行")
@PostMapping(value = "/reconciliation/task")
public ApiResponse<ReconciliationTaskData> reconciliationTask(@Valid @RequestBody ProgramGetDto programGetDto) {
    return ApiResponse.ok(orderTaskService.reconciliationTask(programGetDto.getId()));
}
```

**ReconciliationTask对账任务实现：**

```java
@Component
public class ReconciliationTask {

    @Autowired
    private OrderService orderService;

    @Autowired
    private ProgramClient programClient;

    @Autowired
    private RedisCache redisCache;

    /**
     * 执行对账任务
     */
    public ReconciliationTaskData executeReconciliation(Long programId) {

        log.info("开始执行对账任务 programId : {}", programId);

        ReconciliationTaskData taskData = new ReconciliationTaskData();
        taskData.setProgramId(programId);
        taskData.setStartTime(DateUtils.now());

        try {
            // 1. 对账订单数据
            OrderReconciliationResult orderResult = reconcileOrderData(programId);
            taskData.setOrderReconciliationResult(orderResult);

            // 2. 对账座位数据
            SeatReconciliationResult seatResult = reconcileSeatData(programId);
            taskData.setSeatReconciliationResult(seatResult);

            // 3. 对账库存数据
            StockReconciliationResult stockResult = reconcileStockData(programId);
            taskData.setStockReconciliationResult(stockResult);

            // 4. 生成对账报告
            generateReconciliationReport(taskData);

            taskData.setEndTime(DateUtils.now());
            taskData.setStatus(ReconciliationStatus.SUCCESS);

            log.info("对账任务执行成功 programId : {} duration : {}ms",
                    programId, taskData.getEndTime().getTime() - taskData.getStartTime().getTime());

        } catch (Exception e) {
            taskData.setEndTime(DateUtils.now());
            taskData.setStatus(ReconciliationStatus.FAILED);
            taskData.setErrorMessage(e.getMessage());

            log.error("对账任务执行失败 programId : {}", programId, e);
        }

        return taskData;
    }

    /**
     * 对账订单数据
     */
    private OrderReconciliationResult reconcileOrderData(Long programId) {

        OrderReconciliationResult result = new OrderReconciliationResult();

        // 1. 统计数据库中的订单数据
        OrderStatistics dbOrderStats = orderService.getOrderStatistics(programId);
        result.setDbOrderStats(dbOrderStats);

        // 2. 统计Redis缓存中的订单数据
        OrderStatistics cacheOrderStats = getOrderStatisticsFromCache(programId);
        result.setCacheOrderStats(cacheOrderStats);

        // 3. 对比数据差异
        List<OrderDataDifference> differences = compareOrderData(dbOrderStats, cacheOrderStats);
        result.setDifferences(differences);

        // 4. 修复数据不一致
        if (CollectionUtil.isNotEmpty(differences)) {
            repairOrderDataInconsistency(programId, differences);
            result.setRepaired(true);
        }

        return result;
    }

    /**
     * 对账座位数据
     */
    private SeatReconciliationResult reconcileSeatData(Long programId) {

        SeatReconciliationResult result = new SeatReconciliationResult();

        try {
            // 1. 获取节目的所有票档
            ApiResponse<List<TicketCategoryVo>> ticketCategoryResponse =
                    programClient.getTicketCategoryList(programId);

            if (!Objects.equals(ticketCategoryResponse.getCode(), BaseCode.SUCCESS.getCode())) {
                throw new DaMaiFrameException(BaseCode.GET_TICKET_CATEGORY_FAIL);
            }

            List<TicketCategoryVo> ticketCategoryList = ticketCategoryResponse.getData();

            for (TicketCategoryVo ticketCategory : ticketCategoryList) {

                // 2. 对比数据库和Redis中的座位数据
                SeatDataComparison comparison = compareSeatData(programId, ticketCategory.getId());
                result.addSeatDataComparison(comparison);

                // 3. 修复座位数据不一致
                if (comparison.hasInconsistency()) {
                    repairSeatDataInconsistency(programId, ticketCategory.getId(), comparison);
                }
            }

        } catch (Exception e) {
            log.error("座位数据对账失败 programId : {}", programId, e);
            throw e;
        }

        return result;
    }

    /**
     * 对账库存数据
     */
    private StockReconciliationResult reconcileStockData(Long programId) {

        StockReconciliationResult result = new StockReconciliationResult();

        try {
            // 1. 获取节目的所有票档
            ApiResponse<List<TicketCategoryVo>> ticketCategoryResponse =
                    programClient.getTicketCategoryList(programId);

            List<TicketCategoryVo> ticketCategoryList = ticketCategoryResponse.getData();

            for (TicketCategoryVo ticketCategory : ticketCategoryList) {

                // 2. 计算实际剩余库存
                int actualRemainStock = calculateActualRemainStock(programId, ticketCategory.getId());

                // 3. 获取Redis中的库存数据
                int cacheRemainStock = getCacheRemainStock(programId, ticketCategory.getId());

                // 4. 对比库存差异
                if (actualRemainStock != cacheRemainStock) {
                    StockDataDifference difference = new StockDataDifference();
                    difference.setTicketCategoryId(ticketCategory.getId());
                    difference.setActualRemainStock(actualRemainStock);
                    difference.setCacheRemainStock(cacheRemainStock);
                    difference.setDifference(actualRemainStock - cacheRemainStock);

                    result.addStockDataDifference(difference);

                    // 5. 修复库存数据
                    repairStockData(programId, ticketCategory.getId(), actualRemainStock);
                }
            }

        } catch (Exception e) {
            log.error("库存数据对账失败 programId : {}", programId, e);
            throw e;
        }

        return result;
    }

    private int calculateActualRemainStock(Long programId, Long ticketCategoryId) {
        // 1. 获取票档总库存
        int totalStock = getTotalStock(programId, ticketCategoryId);

        // 2. 统计已售出的票数
        int soldCount = orderService.getSoldTicketCount(programId, ticketCategoryId);

        // 3. 统计锁定中的票数
        int lockedCount = getLockedTicketCount(programId, ticketCategoryId);

        // 4. 计算实际剩余库存
        return totalStock - soldCount - lockedCount;
    }

    private void repairStockData(Long programId, Long ticketCategoryId, int actualRemainStock) {
        try {
            // 更新Redis中的库存数据
            String stockKey = RedisKeyBuild.createRedisKey(
                    RedisKeyManage.PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION,
                    programId, ticketCategoryId).getRelKey();

            redisCache.hset(stockKey, String.valueOf(ticketCategoryId), String.valueOf(actualRemainStock));

            log.info("库存数据修复成功 programId : {} ticketCategoryId : {} actualRemainStock : {}",
                    programId, ticketCategoryId, actualRemainStock);

        } catch (Exception e) {
            log.error("库存数据修复失败 programId : {} ticketCategoryId : {}",
                    programId, ticketCategoryId, e);
        }
    }
}
```

**对账任务调度：**

```java
@Component
public class ReconciliationScheduler {

    @Autowired
    private ReconciliationTask reconciliationTask;

    @Autowired
    private ProgramService programService;

    /**
     * 每小时执行一次对账任务
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void scheduleReconciliation() {

        log.info("开始执行定时对账任务");

        try {
            // 1. 获取需要对账的节目列表
            List<Long> programIds = programService.getActivePrograms();

            // 2. 并行执行对账任务
            List<CompletableFuture<Void>> futures = programIds.stream()
                    .map(programId -> CompletableFuture.runAsync(() -> {
                        try {
                            reconciliationTask.executeReconciliation(programId);
                        } catch (Exception e) {
                            log.error("节目对账任务执行失败 programId : {}", programId, e);
                        }
                    }, BusinessThreadPool.getExecutor()))
                    .collect(Collectors.toList());

            // 3. 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("定时对账任务执行完成 programCount : {}", programIds.size());

        } catch (Exception e) {
            log.error("定时对账任务执行异常", e);
        }
    }
}
```

## 7. 核心组件深度剖析

### 7.1 damai-thread-pool-framework线程池框架深度解析

**框架架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        damai-thread-pool-framework                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │BaseThread   │───▶│BusinessThread│───▶│TraceId      │───▶│Custom       │      │
│  │Pool         │    │Pool          │    │Context      │    │Rejection    │      │
│  │             │    │              │    │Propagation  │    │Handler      │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │线程池基础   │    │业务线程池   │    │链路追踪     │    │拒绝策略     │      │
│  │配置管理     │    │动态配置     │    │上下文传递   │    │自定义处理   │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**BaseThreadPool基础线程池：**

```java
public abstract class BaseThreadPool {

    /**
     * 获取当前线程的上下文信息
     */
    protected static Map<String, Object> getContextForTask() {
        Map<String, Object> context = new HashMap<>();

        // 1. 获取TraceId
        String traceId = MDC.get("traceId");
        if (StringUtil.isNotEmpty(traceId)) {
            context.put("traceId", traceId);
        }

        // 2. 获取用户信息
        String userId = getCurrentUserId();
        if (StringUtil.isNotEmpty(userId)) {
            context.put("userId", userId);
        }

        // 3. 获取请求信息
        HttpServletRequest request = getCurrentRequest();
        if (Objects.nonNull(request)) {
            context.put("requestUri", request.getRequestURI());
            context.put("requestMethod", request.getMethod());
        }

        return context;
    }

    /**
     * 获取需要保持的上下文信息
     */
    protected static Map<String, Object> getContextForHold() {
        Map<String, Object> context = new HashMap<>();

        // 保持Spring Security上下文
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (Objects.nonNull(authentication)) {
            context.put("authentication", authentication);
        }

        return context;
    }

    /**
     * 包装Runnable任务，传递上下文
     */
    protected static Runnable wrapTask(Runnable task,
                                      Map<String, Object> contextForTask,
                                      Map<String, Object> contextForHold) {
        return () -> {
            // 1. 设置上下文
            setContext(contextForTask, contextForHold);

            try {
                // 2. 执行任务
                task.run();
            } finally {
                // 3. 清理上下文
                clearContext();
            }
        };
    }

    /**
     * 包装Callable任务，传递上下文
     */
    protected static <T> Callable<T> wrapTask(Callable<T> task,
                                             Map<String, Object> contextForTask,
                                             Map<String, Object> contextForHold) {
        return () -> {
            // 1. 设置上下文
            setContext(contextForTask, contextForHold);

            try {
                // 2. 执行任务
                return task.call();
            } finally {
                // 3. 清理上下文
                clearContext();
            }
        };
    }

    private static void setContext(Map<String, Object> contextForTask, Map<String, Object> contextForHold) {
        // 设置MDC上下文
        if (contextForTask.containsKey("traceId")) {
            MDC.put("traceId", (String) contextForTask.get("traceId"));
        }

        if (contextForTask.containsKey("userId")) {
            MDC.put("userId", (String) contextForTask.get("userId"));
        }

        // 设置Spring Security上下文
        if (contextForHold.containsKey("authentication")) {
            Authentication authentication = (Authentication) contextForHold.get("authentication");
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
    }

    private static void clearContext() {
        // 清理MDC
        MDC.clear();

        // 清理Spring Security上下文
        SecurityContextHolder.clearContext();
    }
}
```

**BusinessThreadPool业务线程池：**

```java
public class BusinessThreadPool extends BaseThreadPool {

    private static ThreadPoolExecutor executor = null;

    static {
        executor = new ThreadPoolExecutor(
                // 核心线程数：CPU核心数 + 1
                Runtime.getRuntime().availableProcessors() + 1,

                // 最大线程数：CPU核心数 / 0.2（IO密集型）
                maximumPoolSize(),

                // 空闲时间：60秒
                60,
                TimeUnit.SECONDS,

                // 阻塞队列：有界队列，防止内存溢出
                new ArrayBlockingQueue<>(600),

                // 线程工厂：自定义线程命名
                new BusinessNameThreadFactory(),

                // 拒绝策略：自定义拒绝策略
                new ThreadPoolRejectedExecutionHandler.BusinessAbortPolicy());
    }

    /**
     * 计算最大线程数
     * IO密集型：CPU核心数 / 0.2
     * CPU密集型：CPU核心数 + 1
     */
    private static Integer maximumPoolSize() {
        return new BigDecimal(Runtime.getRuntime().availableProcessors())
                .divide(new BigDecimal("0.2"), 0, RoundingMode.HALF_UP)
                .intValue();
    }

    /**
     * 执行Runnable任务
     */
    public static void execute(Runnable task) {
        executor.execute(wrapTask(task, getContextForTask(), getContextForHold()));
    }

    /**
     * 提交Callable任务
     */
    public static <T> Future<T> submit(Callable<T> task) {
        return executor.submit(wrapTask(task, getContextForTask(), getContextForHold()));
    }

    /**
     * 获取线程池执行器（用于CompletableFuture）
     */
    public static Executor getExecutor() {
        return executor;
    }

    /**
     * 获取线程池状态信息
     */
    public static ThreadPoolStatus getStatus() {
        ThreadPoolStatus status = new ThreadPoolStatus();
        status.setCorePoolSize(executor.getCorePoolSize());
        status.setMaximumPoolSize(executor.getMaximumPoolSize());
        status.setActiveCount(executor.getActiveCount());
        status.setTaskCount(executor.getTaskCount());
        status.setCompletedTaskCount(executor.getCompletedTaskCount());
        status.setQueueSize(executor.getQueue().size());
        status.setQueueRemainingCapacity(executor.getQueue().remainingCapacity());

        return status;
    }
}
```

**BusinessNameThreadFactory线程工厂：**

```java
public class BusinessNameThreadFactory extends AbstractNameThreadFactory {

    public BusinessNameThreadFactory() {
        super("business-thread-");
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = super.newThread(r);

        // 设置线程为非守护线程
        thread.setDaemon(false);

        // 设置线程优先级
        thread.setPriority(Thread.NORM_PRIORITY);

        // 设置未捕获异常处理器
        thread.setUncaughtExceptionHandler((t, e) -> {
            log.error("线程执行异常 threadName : {} error : ", t.getName(), e);

            // 发送告警通知
            sendAlertNotification(t.getName(), e);
        });

        return thread;
    }

    private void sendAlertNotification(String threadName, Throwable e) {
        try {
            // 构建告警信息
            AlertMessage alertMessage = new AlertMessage();
            alertMessage.setLevel(AlertLevel.ERROR);
            alertMessage.setTitle("线程池异常告警");
            alertMessage.setContent(String.format("线程 %s 执行异常：%s", threadName, e.getMessage()));
            alertMessage.setTimestamp(new Date());

            // 发送告警（可以是邮件、短信、钉钉等）
            alertService.sendAlert(alertMessage);

        } catch (Exception ex) {
            log.error("发送线程异常告警失败", ex);
        }
    }
}
```

**自定义拒绝策略：**

```java
public class ThreadPoolRejectedExecutionHandler {

    /**
     * 业务拒绝策略：记录日志 + 抛出异常
     */
    public static class BusinessAbortPolicy implements RejectedExecutionHandler {

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {

            // 1. 记录拒绝日志
            log.error("线程池任务被拒绝 activeCount : {} queueSize : {} taskCount : {}",
                    executor.getActiveCount(),
                    executor.getQueue().size(),
                    executor.getTaskCount());

            // 2. 发送告警
            sendRejectionAlert(executor);

            // 3. 抛出异常
            throw new RejectedExecutionException("线程池队列已满，任务被拒绝执行");
        }

        private void sendRejectionAlert(ThreadPoolExecutor executor) {
            try {
                AlertMessage alertMessage = new AlertMessage();
                alertMessage.setLevel(AlertLevel.CRITICAL);
                alertMessage.setTitle("线程池拒绝任务告警");
                alertMessage.setContent(String.format(
                        "线程池负载过高，任务被拒绝执行。活跃线程数：%d，队列大小：%d，总任务数：%d",
                        executor.getActiveCount(),
                        executor.getQueue().size(),
                        executor.getTaskCount()));
                alertMessage.setTimestamp(new Date());

                alertService.sendAlert(alertMessage);

            } catch (Exception e) {
                log.error("发送线程池拒绝告警失败", e);
            }
        }
    }

    /**
     * 调用者运行策略：在调用线程中执行任务
     */
    public static class BusinessCallerRunsPolicy implements RejectedExecutionHandler {

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            if (!executor.isShutdown()) {
                log.warn("线程池队列已满，任务在调用线程中执行");
                r.run();
            }
        }
    }
}
```

**线程池监控：**

```java
@Component
public class ThreadPoolMonitor {

    @Autowired
    private MeterRegistry meterRegistry;

    @PostConstruct
    public void initMetrics() {
        // 注册线程池监控指标
        Gauge.builder("thread.pool.active.count")
                .description("线程池活跃线程数")
                .register(meterRegistry, this, ThreadPoolMonitor::getActiveCount);

        Gauge.builder("thread.pool.queue.size")
                .description("线程池队列大小")
                .register(meterRegistry, this, ThreadPoolMonitor::getQueueSize);

        Gauge.builder("thread.pool.completed.task.count")
                .description("线程池已完成任务数")
                .register(meterRegistry, this, ThreadPoolMonitor::getCompletedTaskCount);
    }

    private double getActiveCount(ThreadPoolMonitor monitor) {
        return BusinessThreadPool.getStatus().getActiveCount();
    }

    private double getQueueSize(ThreadPoolMonitor monitor) {
        return BusinessThreadPool.getStatus().getQueueSize();
    }

    private double getCompletedTaskCount(ThreadPoolMonitor monitor) {
        return BusinessThreadPool.getStatus().getCompletedTaskCount();
    }

    /**
     * 定时检查线程池状态
     */
    @Scheduled(fixedRate = 30000) // 30秒检查一次
    public void checkThreadPoolStatus() {
        ThreadPoolStatus status = BusinessThreadPool.getStatus();

        // 检查队列使用率
        double queueUsageRate = (double) status.getQueueSize() /
                (status.getQueueSize() + status.getQueueRemainingCapacity());

        if (queueUsageRate > 0.8) {
            log.warn("线程池队列使用率过高 queueUsageRate : {}", queueUsageRate);

            // 发送告警
            sendQueueUsageAlert(queueUsageRate);
        }

        // 检查活跃线程比例
        double activeThreadRate = (double) status.getActiveCount() / status.getMaximumPoolSize();

        if (activeThreadRate > 0.9) {
            log.warn("线程池活跃线程比例过高 activeThreadRate : {}", activeThreadRate);

            // 发送告警
            sendActiveThreadAlert(activeThreadRate);
        }
    }
}
```

### 7.2 damai-redis-common-framework Redis框架深度解析

**Redis框架架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      damai-redis-common-framework                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │RedisCache   │───▶│RedisKeyBuild│───▶│Multi-Level  │───▶│Serialization│      │
│  │Interface    │    │Key Builder  │    │Cache        │    │Strategy     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │统一缓存接口 │    │键值构建器   │    │多级缓存策略 │    │序列化优化   │      │
│  │操作抽象     │    │命名规范     │    │本地+分布式  │    │JSON/Protobuf│      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**RedisCache核心接口设计：**

```java
public interface RedisCache {

    // ==================== 基础操作 ====================

    /**
     * 获取对象，支持缓存穿透保护
     */
    <T> T get(RedisKeyBuild redisKeyBuild, Class<T> clazz, Supplier<T> supplier, long ttl, TimeUnit timeUnit);

    /**
     * 设置对象，支持过期时间
     */
    void set(RedisKeyBuild redisKeyBuild, Object object, long ttl, TimeUnit timeUnit);

    /**
     * 删除键
     */
    void del(RedisKeyBuild redisKeyBuild);

    /**
     * 检查键是否存在
     */
    Boolean hasKey(RedisKeyBuild redisKeyBuild);

    /**
     * 设置过期时间
     */
    Boolean expire(RedisKeyBuild redisKeyBuild, long ttl, TimeUnit timeUnit);

    // ==================== Hash操作 ====================

    /**
     * Hash设置
     */
    void hset(RedisKeyBuild redisKeyBuild, String field, Object value);

    /**
     * Hash批量设置
     */
    void hmset(RedisKeyBuild redisKeyBuild, Map<String, Object> map);

    /**
     * Hash获取
     */
    <T> T hget(RedisKeyBuild redisKeyBuild, String field, Class<T> clazz);

    /**
     * Hash删除字段
     */
    Long hdel(RedisKeyBuild redisKeyBuild, String... fields);

    /**
     * Hash增量操作
     */
    Long hincrby(RedisKeyBuild redisKeyBuild, String field, long increment);

    // ==================== List操作 ====================

    /**
     * 左侧推入
     */
    Long leftPushForList(RedisKeyBuild redisKeyBuild, Object value);

    /**
     * 右侧推入
     */
    Long rightPushForList(RedisKeyBuild redisKeyBuild, Object value);

    /**
     * 左侧弹出
     */
    <T> T leftPopForList(RedisKeyBuild redisKeyBuild, Class<T> clazz);

    /**
     * 获取列表范围
     */
    <T> List<T> getValueIsListByRange(RedisKeyBuild redisKeyBuild, long start, long end, Class<T> clazz);

    // ==================== Set操作 ====================

    /**
     * 添加到集合
     */
    Long addForSet(RedisKeyBuild redisKeyBuild, Object value);

    /**
     * 从集合移除
     */
    Long removeForSet(RedisKeyBuild redisKeyBuild, Object value);

    /**
     * 检查是否是集合成员
     */
    Boolean isMemberForSet(RedisKeyBuild redisKeyBuild, Object value);

    // ==================== 分布式锁操作 ====================

    /**
     * 尝试获取锁
     */
    Boolean tryLock(RedisKeyBuild redisKeyBuild, String value, long ttl, TimeUnit timeUnit);

    /**
     * 释放锁
     */
    Boolean releaseLock(RedisKeyBuild redisKeyBuild, String value);

    // ==================== 原子操作 ====================

    /**
     * 增量操作
     */
    Long incrBy(RedisKeyBuild redisKeyBuild, long increment);

    /**
     * 减量操作
     */
    Long decrBy(RedisKeyBuild redisKeyBuild, long decrement);

    // ==================== 批量操作 ====================

    /**
     * 批量获取
     */
    <T> List<T> multiGet(Collection<RedisKeyBuild> keys, Class<T> clazz);

    /**
     * 批量设置
     */
    void multiSet(Map<RedisKeyBuild, Object> map);

    /**
     * 批量删除
     */
    void multiDel(Collection<RedisKeyBuild> keys);

    // ==================== 管道操作 ====================

    /**
     * 执行管道操作
     */
    List<Object> executePipelined(RedisCallback<Object> action);

    // ==================== Lua脚本操作 ====================

    /**
     * 执行Lua脚本
     */
    <T> T execute(RedisScript<T> script, List<String> keys, Object... args);

    /**
     * 获取Redis实例（用于复杂操作）
     */
    StringRedisTemplate getInstance();
}
```

**RedisCacheImpl核心实现：**

```java
@AllArgsConstructor
public class RedisCacheImpl implements RedisCache {

    private StringRedisTemplate redisTemplate;

    @Override
    public <T> T get(RedisKeyBuild redisKeyBuild, Class<T> clazz, Supplier<T> supplier, long ttl, TimeUnit timeUnit) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        // 1. 尝试从Redis获取
        String cachedValue = redisTemplate.opsForValue().get(key);

        if (StringUtil.isNotEmpty(cachedValue)) {
            // 2. 缓存命中，反序列化返回
            if (String.class.isAssignableFrom(clazz)) {
                return (T) cachedValue;
            }
            return getComplex(cachedValue, clazz);
        }

        // 3. 缓存未命中，执行supplier
        if (Objects.nonNull(supplier)) {
            T result = supplier.get();

            if (Objects.nonNull(result)) {
                // 4. 将结果缓存
                set(redisKeyBuild, result, ttl, timeUnit);
            } else {
                // 5. 缓存空值，防止缓存穿透
                setCacheNullValue(redisKeyBuild, ttl, timeUnit);
            }

            return result;
        }

        return null;
    }

    @Override
    public void set(RedisKeyBuild redisKeyBuild, Object object, long ttl, TimeUnit timeUnit) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        // 序列化对象
        String json = object instanceof String ? (String) object : JSON.toJSONString(object);

        if (ttl > 0) {
            redisTemplate.opsForValue().set(key, json, ttl, timeUnit);
        } else {
            redisTemplate.opsForValue().set(key, json);
        }
    }

    @Override
    public void hset(RedisKeyBuild redisKeyBuild, String field, Object value) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        String jsonValue = value instanceof String ? (String) value : JSON.toJSONString(value);
        redisTemplate.opsForHash().put(key, field, jsonValue);
    }

    @Override
    public void hmset(RedisKeyBuild redisKeyBuild, Map<String, Object> map) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        CacheUtil.checkNotEmpty(map);
        String key = redisKeyBuild.getRelKey();

        // 批量序列化
        Map<String, String> serializedMap = map.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() instanceof String ?
                                (String) entry.getValue() :
                                JSON.toJSONString(entry.getValue())
                ));

        redisTemplate.opsForHash().putAll(key, serializedMap);
    }

    @Override
    public <T> T hget(RedisKeyBuild redisKeyBuild, String field, Class<T> clazz) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        Object value = redisTemplate.opsForHash().get(key, field);
        if (Objects.isNull(value)) {
            return null;
        }

        String stringValue = String.valueOf(value);
        if (String.class.isAssignableFrom(clazz)) {
            return (T) stringValue;
        }

        return getComplex(stringValue, clazz);
    }

    @Override
    public Long hincrby(RedisKeyBuild redisKeyBuild, String field, long increment) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    @Override
    public Long leftPushForList(RedisKeyBuild redisKeyBuild, Object value) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        String jsonValue = value instanceof String ? (String) value : JSON.toJSONString(value);
        return redisTemplate.opsForList().leftPush(key, jsonValue);
    }

    @Override
    public <T> List<T> getValueIsListByRange(RedisKeyBuild redisKeyBuild, long start, long end, Class<T> clazz) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        List<String> values = redisTemplate.opsForList().range(key, start, end);
        if (CollectionUtil.isEmpty(values)) {
            return new ArrayList<>();
        }

        return values.stream()
                .map(value -> {
                    if (String.class.isAssignableFrom(clazz)) {
                        return (T) value;
                    }
                    return getComplex(value, clazz);
                })
                .collect(Collectors.toList());
    }

    @Override
    public Long incrBy(RedisKeyBuild redisKeyBuild, long increment) {
        CacheUtil.checkNotBlank(redisKeyBuild);
        String key = redisKeyBuild.getRelKey();

        return redisTemplate.opsForValue().increment(key, increment);
    }

    @Override
    public Long decrBy(RedisKeyBuild redisKeyBuild, long decrement) {
        return incrBy(redisKeyBuild, -decrement);
    }

    @Override
    public <T> List<T> multiGet(Collection<RedisKeyBuild> keys, Class<T> clazz) {
        CacheUtil.checkNotEmpty(keys);

        List<String> keyList = keys.stream()
                .map(RedisKeyBuild::getRelKey)
                .collect(Collectors.toList());

        List<String> values = redisTemplate.opsForValue().multiGet(keyList);
        if (CollectionUtil.isEmpty(values)) {
            return new ArrayList<>();
        }

        return values.stream()
                .map(value -> {
                    if (StringUtil.isEmpty(value)) {
                        return null;
                    }
                    if (String.class.isAssignableFrom(clazz)) {
                        return (T) value;
                    }
                    return getComplex(value, clazz);
                })
                .collect(Collectors.toList());
    }

    @Override
    public void multiSet(Map<RedisKeyBuild, Object> map) {
        CacheUtil.checkNotEmpty(map);

        Map<String, String> serializedMap = map.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().getRelKey(),
                        entry -> entry.getValue() instanceof String ?
                                (String) entry.getValue() :
                                JSON.toJSONString(entry.getValue())
                ));

        redisTemplate.opsForValue().multiSet(serializedMap);
    }

    @Override
    public List<Object> executePipelined(RedisCallback<Object> action) {
        return redisTemplate.executePipelined(action);
    }

    @Override
    public <T> T execute(RedisScript<T> script, List<String> keys, Object... args) {
        return redisTemplate.execute(script, keys, args);
    }

    /**
     * 复杂对象反序列化
     */
    private <T> T getComplex(String cachedValue, Class<T> clazz) {
        try {
            if (StringUtil.isEmpty(cachedValue)) {
                return null;
            }

            // 检查是否是空值标记
            if (CacheUtil.NULL_VALUE.equals(cachedValue)) {
                return null;
            }

            return JSON.parseObject(cachedValue, clazz);

        } catch (Exception e) {
            log.error("Redis缓存反序列化失败 cachedValue : {} clazz : {}", cachedValue, clazz.getName(), e);
            return null;
        }
    }

    /**
     * 设置空值缓存，防止缓存穿透
     */
    private void setCacheNullValue(RedisKeyBuild redisKeyBuild, long ttl, TimeUnit timeUnit) {
        // 空值缓存时间较短，避免长时间缓存空值
        long nullValueTtl = Math.min(ttl, 300); // 最多5分钟
        set(redisKeyBuild, CacheUtil.NULL_VALUE, nullValueTtl, timeUnit);
    }
}
```

**RedisKeyBuild键构建器：**

```java
public class RedisKeyBuild {

    private String relKey;

    private RedisKeyBuild(String relKey) {
        this.relKey = relKey;
    }

    /**
     * 创建Redis键
     */
    public static RedisKeyBuild createRedisKey(RedisKeyManage redisKeyManage, Object... params) {
        String key = buildKey(redisKeyManage.getKey(), params);
        return new RedisKeyBuild(key);
    }

    /**
     * 构建键值
     */
    private static String buildKey(String template, Object... params) {
        if (Objects.isNull(params) || params.length == 0) {
            return template;
        }

        StringBuilder keyBuilder = new StringBuilder(template);
        for (Object param : params) {
            keyBuilder.append("_").append(param);
        }

        return keyBuilder.toString();
    }

    public String getRelKey() {
        return relKey;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RedisKeyBuild that = (RedisKeyBuild) o;
        return Objects.equals(relKey, that.relKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(relKey);
    }
}
```

**RedisKeyManage键管理枚举：**

```java
public enum RedisKeyManage {

    // ==================== 节目相关 ====================
    PROGRAM_DETAIL("program_info", "节目详情"),
    PROGRAM_TICKET_REMAIN_NUMBER_HASH_RESOLUTION("ticket_remain_number", "余票数量"),
    SEAT_NO_SOLD_HASH_RESOLUTION("seat_no_sold", "未售出座位"),
    SEAT_LOCK_HASH_RESOLUTION("seat_lock", "锁定座位"),
    SEAT_SOLD_HASH_RESOLUTION("seat_sold", "已售出座位"),

    // ==================== 订单相关 ====================
    ORDER_MQ("order_mq", "订单MQ标记"),
    ACCOUNT_ORDER_COUNT("account_order_count", "用户购票数量"),
    DISCARD_ORDER("discard_order", "丢弃订单"),

    // ==================== 用户相关 ====================
    TICKET_USER_LIST("ticket_user_list", "购票人列表"),
    USER_SESSION("user_session", "用户会话"),

    // ==================== 系统相关 ====================
    REPEAT_EXECUTE_LIMIT("repeat_execute_limit", "重复执行限制"),
    API_RATE_LIMIT("api_rate_limit", "API限流"),
    BLOOM_FILTER("bloom_filter", "布隆过滤器"),

    // ==================== 业务记录 ====================
    PROGRAM_RECORD("program_record", "节目购票记录"),

    ;

    private String key;
    private String description;

    RedisKeyManage(String key, String description) {
        this.key = key;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }
}
```

**CacheUtil缓存工具类：**

```java
public class CacheUtil {

    public static final String NULL_VALUE = "NULL_VALUE";
    public static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.MINUTES;

    /**
     * 检查RedisKeyBuild不为空
     */
    public static void checkNotBlank(RedisKeyBuild redisKeyBuild) {
        if (Objects.isNull(redisKeyBuild) || StringUtil.isEmpty(redisKeyBuild.getRelKey())) {
            throw new IllegalArgumentException("RedisKeyBuild不能为空");
        }
    }

    /**
     * 检查集合不为空
     */
    public static void checkNotEmpty(Collection<?> collection) {
        if (CollectionUtil.isEmpty(collection)) {
            throw new IllegalArgumentException("集合不能为空");
        }
    }

    /**
     * 检查Map不为空
     */
    public static void checkNotEmpty(Map<?, ?> map) {
        if (Objects.isNull(map) || map.isEmpty()) {
            throw new IllegalArgumentException("Map不能为空");
        }
    }

    /**
     * 获取批量键列表
     */
    public static List<String> getBatchKey(Collection<RedisKeyBuild> keys) {
        return keys.stream()
                .map(RedisKeyBuild::getRelKey)
                .collect(Collectors.toList());
    }

    /**
     * 生成缓存键
     */
    public static String generateCacheKey(String prefix, Object... params) {
        StringBuilder keyBuilder = new StringBuilder(prefix);
        for (Object param : params) {
            keyBuilder.append(":").append(param);
        }
        return keyBuilder.toString();
    }

    /**
     * 计算缓存过期时间（添加随机值避免缓存雪崩）
     */
    public static long calculateExpireTime(long baseTtl, TimeUnit timeUnit) {
        long baseTtlSeconds = timeUnit.toSeconds(baseTtl);

        // 添加10%-20%的随机时间
        Random random = new Random();
        long randomSeconds = (long) (baseTtlSeconds * (0.1 + random.nextDouble() * 0.1));

        return baseTtlSeconds + randomSeconds;
    }
}
```

### 7.3 damai-service-delay-queue-framework延迟队列框架深度解析

**延迟队列框架架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    damai-service-delay-queue-framework                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │DelayQueue   │───▶│DelayProduce │───▶│DelayConsumer│───▶│ConsumerTask │      │
│  │Context      │    │Queue        │    │Queue        │    │Interface    │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │队列管理     │    │消息生产     │    │消息消费     │    │业务处理     │      │
│  │Topic路由    │    │延迟投递     │    │监听启动     │    │任务执行     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**DelayBaseQueue基础队列：**

```java
@Slf4j
public class DelayBaseQueue {

    protected final RedissonClient redissonClient;
    protected final RBlockingQueue<String> blockingQueue;

    public DelayBaseQueue(RedissonClient redissonClient, String relTopic) {
        this.redissonClient = redissonClient;
        this.blockingQueue = redissonClient.getBlockingQueue(relTopic);

        log.info("DelayBaseQueue初始化完成 topic : {}", relTopic);
    }

    /**
     * 获取队列大小
     */
    public int size() {
        return blockingQueue.size();
    }

    /**
     * 清空队列
     */
    public void clear() {
        blockingQueue.clear();
    }

    /**
     * 检查队列是否为空
     */
    public boolean isEmpty() {
        return blockingQueue.isEmpty();
    }
}
```

**DelayProduceQueue生产者队列：**

```java
public class DelayProduceQueue extends DelayBaseQueue {

    private final RDelayedQueue<String> delayedQueue;

    public DelayProduceQueue(RedissonClient redissonClient, final String relTopic) {
        super(redissonClient, relTopic);
        this.delayedQueue = redissonClient.getDelayedQueue(blockingQueue);

        log.info("DelayProduceQueue初始化完成 topic : {}", relTopic);
    }

    /**
     * 发送延迟消息
     */
    public void offer(String content, long delayTime, TimeUnit timeUnit) {
        try {
            delayedQueue.offer(content, delayTime, timeUnit);

            log.info("延迟消息发送成功 content : {} delayTime : {} timeUnit : {}",
                    content, delayTime, timeUnit);

        } catch (Exception e) {
            log.error("延迟消息发送失败 content : {} delayTime : {} timeUnit : {}",
                    content, delayTime, timeUnit, e);
            throw new DaMaiFrameException(BaseCode.DELAY_QUEUE_SEND_ERROR, e);
        }
    }

    /**
     * 批量发送延迟消息
     */
    public void offerBatch(List<DelayMessage> messages) {
        try {
            for (DelayMessage message : messages) {
                delayedQueue.offer(message.getContent(), message.getDelayTime(), message.getTimeUnit());
            }

            log.info("批量延迟消息发送成功 messageCount : {}", messages.size());

        } catch (Exception e) {
            log.error("批量延迟消息发送失败 messageCount : {}", messages.size(), e);
            throw new DaMaiFrameException(BaseCode.DELAY_QUEUE_BATCH_SEND_ERROR, e);
        }
    }

    /**
     * 获取延迟队列统计信息
     */
    public DelayQueueStats getStats() {
        DelayQueueStats stats = new DelayQueueStats();
        stats.setQueueSize(blockingQueue.size());
        stats.setDelayedQueueSize(delayedQueue.size());

        return stats;
    }
}
```

**DelayConsumerQueue消费者队列：**

```java
public class DelayConsumerQueue extends DelayBaseQueue {

    private static final AtomicInteger listenStartThreadCount = new AtomicInteger(0);

    private final ThreadPoolExecutor listenStartThreadPool;
    private final ThreadPoolExecutor executeTaskThreadPool;
    private final AtomicBoolean runFlag = new AtomicBoolean(false);

    private ConsumerTask consumerTask;

    public DelayConsumerQueue(DelayQueuePart delayQueuePart, String relTopic) {
        super(delayQueuePart.getDelayQueueBasePart().getRedissonClient(), relTopic);

        // 初始化监听线程池（单线程）
        this.listenStartThreadPool = new ThreadPoolExecutor(
                1, 1, 60, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                r -> new Thread(Thread.currentThread().getThreadGroup(), r,
                        "delay-queue-listen-" + listenStartThreadCount.getAndIncrement())
        );

        // 初始化任务执行线程池
        DelayQueueProperties properties = delayQueuePart.getDelayQueueBasePart().getDelayQueueProperties();
        this.executeTaskThreadPool = new ThreadPoolExecutor(
                properties.getCorePoolSize(),
                properties.getMaximumPoolSize(),
                properties.getKeepAliveTime(),
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(properties.getQueueCapacity()),
                new DelayQueueThreadFactory("delay-queue-execute-"),
                new DelayQueueRejectedExecutionHandler()
        );

        log.info("DelayConsumerQueue初始化完成 topic : {} corePoolSize : {} maxPoolSize : {}",
                relTopic, properties.getCorePoolSize(), properties.getMaximumPoolSize());
    }

    /**
     * 设置消费任务
     */
    public void setConsumerTask(ConsumerTask consumerTask) {
        this.consumerTask = consumerTask;
    }

    /**
     * 启动监听
     */
    public synchronized void listenStart() {
        if (!runFlag.get()) {
            runFlag.set(true);

            listenStartThreadPool.execute(() -> {
                log.info("延迟队列监听启动 topic : {}", consumerTask.topic());

                while (!Thread.interrupted()) {
                    try {
                        // 阻塞获取消息
                        String content = blockingQueue.take();

                        // 异步执行消费任务
                        executeTaskThreadPool.execute(() -> {
                            try {
                                long startTime = System.currentTimeMillis();

                                // 执行业务逻辑
                                consumerTask.execute(content);

                                long endTime = System.currentTimeMillis();
                                log.info("延迟队列消息处理成功 topic : {} content : {} duration : {}ms",
                                        consumerTask.topic(), content, endTime - startTime);

                            } catch (Exception e) {
                                log.error("延迟队列消息处理失败 topic : {} content : {}",
                                        consumerTask.topic(), content, e);

                                // 处理失败，可以考虑重试或死信队列
                                handleConsumerError(content, e);
                            }
                        });

                    } catch (InterruptedException e) {
                        log.info("延迟队列监听被中断 topic : {}", consumerTask.topic());
                        Thread.currentThread().interrupt();
                        break;

                    } catch (Throwable e) {
                        log.error("延迟队列监听异常 topic : {}", consumerTask.topic(), e);

                        // 异常后短暂休眠，避免频繁异常
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }

                log.info("延迟队列监听结束 topic : {}", consumerTask.topic());
            });
        }
    }

    /**
     * 停止监听
     */
    public synchronized void listenStop() {
        if (runFlag.get()) {
            runFlag.set(false);

            // 关闭线程池
            destroy(listenStartThreadPool);
            destroy(executeTaskThreadPool);

            log.info("延迟队列监听停止 topic : {}", consumerTask.topic());
        }
    }

    /**
     * 处理消费异常
     */
    private void handleConsumerError(String content, Exception e) {
        try {
            // 可以实现重试机制或死信队列
            DelayConsumerErrorRecord errorRecord = new DelayConsumerErrorRecord();
            errorRecord.setTopic(consumerTask.topic());
            errorRecord.setContent(content);
            errorRecord.setErrorMessage(e.getMessage());
            errorRecord.setErrorTime(new Date());

            // 记录错误信息（可以存储到数据库或发送告警）
            recordConsumerError(errorRecord);

        } catch (Exception ex) {
            log.error("记录消费异常失败", ex);
        }
    }

    /**
     * 销毁线程池
     */
    public void destroy(ExecutorService executorService) {
        try {
            if (Objects.nonNull(executorService)) {
                executorService.shutdown();

                // 等待任务完成
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            }
        } catch (Exception e) {
            log.error("销毁线程池异常", e);
        }
    }

    /**
     * 获取消费者统计信息
     */
    public DelayConsumerStats getStats() {
        DelayConsumerStats stats = new DelayConsumerStats();
        stats.setQueueSize(blockingQueue.size());
        stats.setActiveThreadCount(executeTaskThreadPool.getActiveCount());
        stats.setCompletedTaskCount(executeTaskThreadPool.getCompletedTaskCount());
        stats.setTaskCount(executeTaskThreadPool.getTaskCount());
        stats.setRunning(runFlag.get());

        return stats;
    }
}
```

**DelayQueueContext延迟队列上下文：**

```java
@Component
public class DelayQueueContext {

    private final Map<String, DelayProduceQueue> delayProduceQueueMap = new ConcurrentHashMap<>();
    private final Map<String, DelayConsumerQueue> delayConsumerQueueMap = new ConcurrentHashMap<>();

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DelayQueueProperties delayQueueProperties;

    /**
     * 发送延迟消息
     */
    public void sendMessage(String topic, String message, long delayTime, TimeUnit timeUnit) {
        DelayProduceQueue delayProduceQueue = delayProduceQueueMap.computeIfAbsent(topic,
                key -> new DelayProduceQueue(redissonClient, key));

        delayProduceQueue.offer(message, delayTime, timeUnit);
    }

    /**
     * 批量发送延迟消息
     */
    public void sendMessageBatch(String topic, List<DelayMessage> messages) {
        DelayProduceQueue delayProduceQueue = delayProduceQueueMap.computeIfAbsent(topic,
                key -> new DelayProduceQueue(redissonClient, key));

        delayProduceQueue.offerBatch(messages);
    }

    /**
     * 注册消费者
     */
    public void registerConsumer(String topic, ConsumerTask consumerTask) {
        DelayConsumerQueue delayConsumerQueue = delayConsumerQueueMap.computeIfAbsent(topic,
                key -> new DelayConsumerQueue(createDelayQueuePart(), key));

        delayConsumerQueue.setConsumerTask(consumerTask);
        delayConsumerQueue.listenStart();

        log.info("延迟队列消费者注册成功 topic : {}", topic);
    }

    /**
     * 注销消费者
     */
    public void unregisterConsumer(String topic) {
        DelayConsumerQueue delayConsumerQueue = delayConsumerQueueMap.get(topic);
        if (Objects.nonNull(delayConsumerQueue)) {
            delayConsumerQueue.listenStop();
            delayConsumerQueueMap.remove(topic);

            log.info("延迟队列消费者注销成功 topic : {}", topic);
        }
    }

    /**
     * 获取所有队列统计信息
     */
    public Map<String, DelayQueueStats> getAllStats() {
        Map<String, DelayQueueStats> statsMap = new HashMap<>();

        // 生产者队列统计
        delayProduceQueueMap.forEach((topic, queue) -> {
            DelayQueueStats stats = queue.getStats();
            stats.setType("PRODUCER");
            statsMap.put(topic + "_PRODUCER", stats);
        });

        // 消费者队列统计
        delayConsumerQueueMap.forEach((topic, queue) -> {
            DelayConsumerStats stats = queue.getStats();
            stats.setType("CONSUMER");
            statsMap.put(topic + "_CONSUMER", stats);
        });

        return statsMap;
    }

    private DelayQueuePart createDelayQueuePart() {
        DelayQueueBasePart basePart = new DelayQueueBasePart();
        basePart.setRedissonClient(redissonClient);
        basePart.setDelayQueueProperties(delayQueueProperties);

        DelayQueuePart delayQueuePart = new DelayQueuePart();
        delayQueuePart.setDelayQueueBasePart(basePart);

        return delayQueuePart;
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("开始清理延迟队列资源");

        // 停止所有消费者
        delayConsumerQueueMap.forEach((topic, queue) -> {
            try {
                queue.listenStop();
            } catch (Exception e) {
                log.error("停止延迟队列消费者失败 topic : {}", topic, e);
            }
        });

        delayConsumerQueueMap.clear();
        delayProduceQueueMap.clear();

        log.info("延迟队列资源清理完成");
    }
}
```

**ConsumerTask消费任务接口：**

```java
public interface ConsumerTask {

    /**
     * 执行消费任务
     * @param content 消息内容
     */
    void execute(String content);

    /**
     * 获取消费者主题
     * @return 主题名称
     */
    String topic();

    /**
     * 获取消费者名称（可选）
     * @return 消费者名称
     */
    default String consumerName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 是否启用（可选）
     * @return 是否启用
     */
    default boolean enabled() {
        return true;
    }
}
```

**DelayQueueProperties配置属性：**

```java
@Data
@ConfigurationProperties(prefix = "damai.delay-queue")
public class DelayQueueProperties {

    /**
     * 核心线程数
     */
    private Integer corePoolSize = 2;

    /**
     * 最大线程数
     */
    private Integer maximumPoolSize = 10;

    /**
     * 线程空闲时间（秒）
     */
    private Long keepAliveTime = 60L;

    /**
     * 队列容量
     */
    private Integer queueCapacity = 1000;

    /**
     * 是否启用延迟队列
     */
    private Boolean enabled = true;

    /**
     * 消费者自动注册
     */
    private Boolean autoRegister = true;

    /**
     * 监控统计间隔（秒）
     */
    private Integer statsInterval = 60;
}
```

**延迟队列在购票系统中的应用：**

1. **订单超时取消**：15分钟后自动取消未支付订单
2. **库存回滚**：订单取消后回滚座位和余票
3. **支付超时处理**：支付超时后的状态检查
4. **数据同步**：定时同步缓存和数据库数据
5. **清理任务**：定时清理过期数据

**延迟队列优势：**

1. **精确延迟**：基于Redisson实现，支持精确的延迟时间控制
2. **高可用性**：基于Redis集群，支持高可用部署
3. **持久化**：消息持久化存储，重启不丢失
4. **监控完善**：提供详细的统计信息和监控指标
5. **易于扩展**：支持动态注册消费者，易于扩展新的延迟任务

### 7.4 damai-service-lock-framework分布式锁框架深度解析

**分布式锁框架架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      damai-service-lock-framework                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │@ServiceLock │───▶│ServiceLock  │───▶│LockType     │───▶│Redisson     │      │
│  │Annotation   │    │Aspect       │    │Strategy     │    │Client       │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │注解驱动     │    │AOP拦截      │    │锁类型选择   │    │底层实现     │      │
│  │参数解析     │    │键值构建     │    │读写/公平锁  │    │Redis分布式  │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**ServiceLockTool分布式锁工具：**

```java
@Component
public class ServiceLockTool {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 获取锁对象
     */
    public RLock getLock(String lockKey, LockType lockType, boolean fair) {
        switch (lockType) {
            case Read:
                return getReadLock(lockKey, fair);
            case Write:
                return getWriteLock(lockKey, fair);
            default:
                throw new IllegalArgumentException("不支持的锁类型: " + lockType);
        }
    }

    /**
     * 获取读锁
     */
    private RLock getReadLock(String lockKey, boolean fair) {
        RReadWriteLock readWriteLock = fair ?
                redissonClient.getFairReadWriteLock(lockKey) :
                redissonClient.getReadWriteLock(lockKey);

        return readWriteLock.readLock();
    }

    /**
     * 获取写锁
     */
    private RLock getWriteLock(String lockKey, boolean fair) {
        if (fair) {
            return redissonClient.getFairLock(lockKey);
        } else {
            return redissonClient.getLock(lockKey);
        }
    }

    /**
     * 尝试获取锁
     */
    public boolean tryLock(String lockKey, LockType lockType, boolean fair,
                          long waitTime, long leaseTime, TimeUnit timeUnit) {
        RLock lock = getLock(lockKey, lockType, fair);

        try {
            return lock.tryLock(waitTime, leaseTime, timeUnit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new DaMaiFrameException(BaseCode.ACQUIRE_LOCK_INTERRUPTED);
        }
    }

    /**
     * 释放锁
     */
    public void releaseLock(String lockKey, LockType lockType, boolean fair) {
        RLock lock = getLock(lockKey, lockType, fair);

        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("释放分布式锁失败 lockKey : {}", lockKey, e);
        }
    }

    /**
     * 获取锁信息
     */
    public LockInfo getLockInfo(String lockKey, LockType lockType, boolean fair) {
        RLock lock = getLock(lockKey, lockType, fair);

        LockInfo lockInfo = new LockInfo();
        lockInfo.setLockKey(lockKey);
        lockInfo.setLockType(lockType);
        lockInfo.setFair(fair);
        lockInfo.setLocked(lock.isLocked());
        lockInfo.setHeldByCurrentThread(lock.isHeldByCurrentThread());
        lockInfo.setHoldCount(lock.getHoldCount());
        lockInfo.setRemainingTime(lock.remainTimeToLive());

        return lockInfo;
    }

    /**
     * 批量获取锁
     */
    public MultiLock getMultiLock(List<String> lockKeys, LockType lockType, boolean fair) {
        RLock[] locks = lockKeys.stream()
                .map(lockKey -> getLock(lockKey, lockType, fair))
                .toArray(RLock[]::new);

        return new MultiLock(locks);
    }

    /**
     * 红锁（RedLock）实现
     */
    public RedissonRedLock getRedLock(List<String> lockKeys, LockType lockType, boolean fair) {
        RLock[] locks = lockKeys.stream()
                .map(lockKey -> getLock(lockKey, lockType, fair))
                .toArray(RLock[]::new);

        return new RedissonRedLock(locks);
    }
}
```

**LockType锁类型枚举：**

```java
public enum LockType {

    /**
     * 读锁
     */
    Read("读锁", "允许多个线程同时读取"),

    /**
     * 写锁
     */
    Write("写锁", "只允许一个线程写入");

    private String name;
    private String description;

    LockType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
}
```

**ServiceLockAspect切面增强：**

```java
@Aspect
@Component
@Slf4j
public class ServiceLockAspect {

    @Autowired
    private ServiceLockTool serviceLockTool;

    @Autowired
    private SpelExpressionParser spelExpressionParser;

    @Around("@annotation(serviceLock)")
    public Object around(ProceedingJoinPoint joinPoint, ServiceLock serviceLock) throws Throwable {

        // 1. 构建锁键
        String lockKey = buildLockKey(joinPoint, serviceLock);

        // 2. 记录锁获取开始时间
        long startTime = System.currentTimeMillis();

        // 3. 尝试获取锁
        boolean acquired = serviceLockTool.tryLock(
                lockKey,
                serviceLock.lockType(),
                serviceLock.fair(),
                serviceLock.waitTime(),
                serviceLock.leaseTime(),
                serviceLock.timeUnit()
        );

        if (!acquired) {
            long waitTime = System.currentTimeMillis() - startTime;
            log.warn("获取分布式锁超时 lockKey : {} waitTime : {}ms", lockKey, waitTime);
            throw new DaMaiFrameException(BaseCode.ACQUIRE_DISTRIBUTED_LOCK_TIMEOUT);
        }

        long acquireTime = System.currentTimeMillis() - startTime;
        log.info("获取分布式锁成功 lockKey : {} acquireTime : {}ms", lockKey, acquireTime);

        try {
            // 4. 执行业务逻辑
            long executeStartTime = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long executeTime = System.currentTimeMillis() - executeStartTime;

            log.info("业务逻辑执行完成 lockKey : {} executeTime : {}ms", lockKey, executeTime);
            return result;

        } finally {
            // 5. 释放锁
            try {
                serviceLockTool.releaseLock(lockKey, serviceLock.lockType(), serviceLock.fair());

                long totalTime = System.currentTimeMillis() - startTime;
                log.info("释放分布式锁成功 lockKey : {} totalTime : {}ms", lockKey, totalTime);

            } catch (Exception e) {
                log.error("释放分布式锁失败 lockKey : {}", lockKey, e);
            }
        }
    }

    /**
     * 构建锁键
     */
    private String buildLockKey(ProceedingJoinPoint joinPoint, ServiceLock serviceLock) {
        StringBuilder lockKeyBuilder = new StringBuilder(serviceLock.name());

        // 解析SpEL表达式
        String[] keys = serviceLock.keys();
        if (keys.length > 0) {
            // 构建SpEL上下文
            EvaluationContext context = buildEvaluationContext(joinPoint);

            for (String key : keys) {
                try {
                    Expression expression = spelExpressionParser.parseExpression(key);
                    Object value = expression.getValue(context);
                    lockKeyBuilder.append(":").append(value);
                } catch (Exception e) {
                    log.error("解析SpEL表达式失败 key : {}", key, e);
                    throw new DaMaiFrameException(BaseCode.SPEL_PARSE_ERROR);
                }
            }
        }

        return lockKeyBuilder.toString();
    }

    /**
     * 构建SpEL评估上下文
     */
    private EvaluationContext buildEvaluationContext(ProceedingJoinPoint joinPoint) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 获取方法参数
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();

        // 将参数添加到上下文
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }

        // 添加常用变量
        context.setVariable("currentTime", System.currentTimeMillis());
        context.setVariable("currentDate", new Date());

        return context;
    }
}
```

**LockInfo锁信息类：**

```java
@Data
public class LockInfo {

    /**
     * 锁键
     */
    private String lockKey;

    /**
     * 锁类型
     */
    private LockType lockType;

    /**
     * 是否公平锁
     */
    private boolean fair;

    /**
     * 是否已锁定
     */
    private boolean locked;

    /**
     * 是否被当前线程持有
     */
    private boolean heldByCurrentThread;

    /**
     * 持有次数
     */
    private int holdCount;

    /**
     * 剩余时间（毫秒）
     */
    private long remainingTime;

    /**
     * 创建时间
     */
    private Date createTime = new Date();
}
```

**MultiLock多重锁实现：**

```java
public class MultiLock {

    private final RLock[] locks;
    private final List<RLock> acquiredLocks = new ArrayList<>();

    public MultiLock(RLock[] locks) {
        this.locks = locks;
    }

    /**
     * 尝试获取所有锁
     */
    public boolean tryLock(long waitTime, long leaseTime, TimeUnit timeUnit) {
        try {
            // 按顺序获取锁，避免死锁
            for (RLock lock : locks) {
                boolean acquired = lock.tryLock(waitTime, leaseTime, timeUnit);
                if (acquired) {
                    acquiredLocks.add(lock);
                } else {
                    // 获取失败，释放已获取的锁
                    releaseAcquiredLocks();
                    return false;
                }
            }
            return true;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            releaseAcquiredLocks();
            return false;
        }
    }

    /**
     * 释放所有已获取的锁
     */
    public void unlock() {
        releaseAcquiredLocks();
    }

    private void releaseAcquiredLocks() {
        // 逆序释放锁
        for (int i = acquiredLocks.size() - 1; i >= 0; i--) {
            RLock lock = acquiredLocks.get(i);
            try {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.error("释放多重锁失败", e);
            }
        }
        acquiredLocks.clear();
    }
}
```

### 7.5 damai-repeat-execute-limit-framework防重复执行框架深度解析

**防重复执行框架架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                  damai-repeat-execute-limit-framework                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │@RepeatExecute│───▶│RepeatExecute│───▶│SpEL         │───▶│Redis        │      │
│  │Limit        │    │LimitAspect  │    │Parser       │    │Cache        │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │注解配置     │    │AOP拦截      │    │动态键构建   │    │分布式标记   │      │
│  │时间窗口     │    │限制检查     │    │参数解析     │    │过期清理     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**RepeatExecuteLimitAspect核心实现：**

```java
@Aspect
@Component
@Slf4j
public class RepeatExecuteLimitAspect {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SpelExpressionParser spelExpressionParser;

    @Around("@annotation(repeatExecuteLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RepeatExecuteLimit repeatExecuteLimit) throws Throwable {

        // 1. 构建限制键
        String limitKey = buildLimitKey(joinPoint, repeatExecuteLimit);

        // 2. 检查是否在限制时间内
        RedisKeyBuild redisKeyBuild = RedisKeyBuild.createRedisKey(RedisKeyManage.REPEAT_EXECUTE_LIMIT, limitKey);

        if (redisCache.hasKey(redisKeyBuild)) {
            log.warn("重复执行被限制 limitKey : {}", limitKey);
            throw new DaMaiFrameException(BaseCode.REPEAT_EXECUTE_LIMIT, repeatExecuteLimit.message());
        }

        // 3. 设置限制标记
        redisCache.set(redisKeyBuild, "1", repeatExecuteLimit.time(), repeatExecuteLimit.timeUnit());

        try {
            // 4. 执行目标方法
            long startTime = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long executeTime = System.currentTimeMillis() - startTime;

            log.info("方法执行成功 limitKey : {} executeTime : {}ms", limitKey, executeTime);
            return result;

        } catch (Exception e) {
            // 5. 异常时删除限制标记，允许重试
            try {
                redisCache.del(redisKeyBuild);
                log.info("异常时删除限制标记 limitKey : {}", limitKey);
            } catch (Exception ex) {
                log.error("删除限制标记失败 limitKey : {}", limitKey, ex);
            }

            throw e;
        }
    }

    /**
     * 构建限制键
     */
    private String buildLimitKey(ProceedingJoinPoint joinPoint, RepeatExecuteLimit repeatExecuteLimit) {
        StringBuilder keyBuilder = new StringBuilder(repeatExecuteLimit.name());

        // 解析SpEL表达式
        String[] keys = repeatExecuteLimit.keys();
        if (keys.length > 0) {
            EvaluationContext context = buildEvaluationContext(joinPoint);

            for (String key : keys) {
                try {
                    Expression expression = spelExpressionParser.parseExpression(key);
                    Object value = expression.getValue(context);
                    keyBuilder.append(":").append(value);
                } catch (Exception e) {
                    log.error("解析SpEL表达式失败 key : {}", key, e);
                    throw new DaMaiFrameException(BaseCode.SPEL_PARSE_ERROR);
                }
            }
        }

        return keyBuilder.toString();
    }

    /**
     * 构建SpEL评估上下文
     */
    private EvaluationContext buildEvaluationContext(ProceedingJoinPoint joinPoint) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 获取方法参数
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();

        // 将参数添加到上下文
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }

        // 添加方法信息
        context.setVariable("methodName", methodSignature.getMethod().getName());
        context.setVariable("className", joinPoint.getTarget().getClass().getSimpleName());

        // 添加时间信息
        context.setVariable("currentTime", System.currentTimeMillis());
        context.setVariable("currentDate", new Date());

        // 添加用户信息（如果存在）
        try {
            String userId = getCurrentUserId();
            if (StringUtil.isNotEmpty(userId)) {
                context.setVariable("currentUserId", userId);
            }
        } catch (Exception e) {
            // 忽略获取用户信息失败
        }

        return context;
    }

    private String getCurrentUserId() {
        // 从请求头或Security上下文获取用户ID
        HttpServletRequest request = getCurrentRequest();
        if (Objects.nonNull(request)) {
            String userId = request.getHeader("X-User-Id");
            if (StringUtil.isNotEmpty(userId)) {
                return userId;
            }
        }

        // 从Spring Security获取
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (Objects.nonNull(authentication) && authentication.isAuthenticated()) {
            return authentication.getName();
        }

        return null;
    }

    private HttpServletRequest getCurrentRequest() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes instanceof ServletRequestAttributes) {
                return ((ServletRequestAttributes) requestAttributes).getRequest();
            }
        } catch (Exception e) {
            // 忽略获取请求失败
        }
        return null;
    }
}
```

## 8. 设计模式与架构思想

### 8.1 策略模式在购票版本管理中的应用

**策略模式实现架构：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              策略模式架构                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ProgramOrder │───▶│ProgramOrder │───▶│Strategy     │───▶│Concrete     │      │
│  │Context      │    │Strategy     │    │Interface    │    │Strategy     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │版本路由     │    │策略抽象     │    │通用接口     │    │具体实现     │      │
│  │动态选择     │    │行为定义     │    │createOrder  │    │V1/V2/V3/V4  │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**策略模式核心优势：**

1. **版本隔离**：每个购票版本独立实现，互不影响
2. **动态切换**：运行时根据配置选择不同策略
3. **易于扩展**：新增版本只需实现接口，无需修改现有代码
4. **测试友好**：可以独立测试每个策略实现

**ProgramOrderStrategy接口设计：**

```java
public interface ProgramOrderStrategy {

    /**
     * 创建订单
     * @param programOrderCreateDto 订单创建参数
     * @return 订单号
     */
    String createOrder(ProgramOrderCreateDto programOrderCreateDto);

    /**
     * 获取策略版本
     * @return 版本号
     */
    default String getVersion() {
        return "unknown";
    }

    /**
     * 获取策略描述
     * @return 描述信息
     */
    default String getDescription() {
        return "购票策略";
    }

    /**
     * 是否启用
     * @return 是否启用
     */
    default boolean isEnabled() {
        return true;
    }

    /**
     * 获取支持的最大并发数
     * @return 最大并发数
     */
    default int getMaxConcurrency() {
        return Integer.MAX_VALUE;
    }
}
```

### 8.2 组合模式在校验体系中的应用

**组合模式实现架构：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              组合模式架构                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                           AbstractComposite                                    │
│                          ┌─────────────────┐                                   │
│                          │ + execute()     │                                   │
│                          │ + allExecute()  │                                   │
│                          │ + addChild()    │                                   │
│                          └─────────────────┘                                   │
│                                    △                                            │
│                                    │                                            │
│                    ┌───────────────┼───────────────┐                           │
│                    │               │               │                           │
│         ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐           │
│         │ParamCheck       │ │DetailCheck      │ │UserExistCheck   │           │
│         │Handler          │ │Handler          │ │Handler          │           │
│         │(Leaf)           │ │(Leaf)           │ │(Leaf)           │           │
│         └─────────────────┘ └─────────────────┘ └─────────────────┘           │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**组合模式核心优势：**

1. **层次化管理**：校验器按层级组织，清晰的执行顺序
2. **统一接口**：所有校验器实现相同接口，便于管理
3. **灵活组合**：可以动态组合不同的校验器
4. **递归处理**：支持嵌套的校验器结构

**AbstractComposite抽象组合类：**

```java
public abstract class AbstractComposite<T> {

    protected List<AbstractComposite<T>> children = new ArrayList<>();

    /**
     * 添加子组件
     */
    public void addChild(AbstractComposite<T> child) {
        children.add(child);
    }

    /**
     * 移除子组件
     */
    public void removeChild(AbstractComposite<T> child) {
        children.remove(child);
    }

    /**
     * 获取所有子组件
     */
    public List<AbstractComposite<T>> getChildren() {
        return new ArrayList<>(children);
    }

    /**
     * 层次化执行所有校验器
     */
    public void allExecute(T param) {
        // 使用广度优先遍历，按层级执行
        Queue<AbstractComposite<T>> queue = new LinkedList<>();
        queue.add(this);

        while (!queue.isEmpty()) {
            int levelSize = queue.size();

            // 同一层级的校验器可以并行执行
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < levelSize; i++) {
                AbstractComposite<T> current = queue.poll();

                // 异步执行当前校验器
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        current.execute(param);
                    } catch (Exception e) {
                        throw new CompletionException(e);
                    }
                }, BusinessThreadPool.getExecutor());

                futures.add(future);

                // 添加子校验器到队列
                queue.addAll(current.children);
            }

            // 等待当前层级所有校验器完成
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } catch (CompletionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof RuntimeException) {
                    throw (RuntimeException) cause;
                } else {
                    throw new DaMaiFrameException(BaseCode.COMPOSITE_EXECUTE_ERROR, cause);
                }
            }
        }
    }

    /**
     * 具体校验逻辑，由子类实现
     */
    protected abstract void execute(T param);

    /**
     * 校验器类型
     */
    public abstract String type();

    /**
     * 父级执行顺序
     */
    public abstract Integer executeParentOrder();

    /**
     * 执行层级
     */
    public abstract Integer executeTier();

    /**
     * 同层级内的执行顺序
     */
    public abstract Integer executeOrder();

    /**
     * 校验器名称
     */
    public String getName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 校验器描述
     */
    public String getDescription() {
        return "校验器";
    }
}
```

### 8.3 观察者模式在事件处理中的应用

**事件驱动架构：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              观察者模式架构                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │Event        │───▶│Event        │───▶│Event        │───▶│Event        │      │
│  │Publisher    │    │Bus          │    │Listener     │    │Handler      │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │事件发布     │    │事件总线     │    │事件监听     │    │事件处理     │      │
│  │异步发送     │    │路由分发     │    │注解驱动     │    │业务逻辑     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**OrderEvent订单事件定义：**

```java
public abstract class OrderEvent {

    private String orderNumber;
    private Long userId;
    private Long programId;
    private Date eventTime;
    private String eventId;

    public OrderEvent(String orderNumber, Long userId, Long programId) {
        this.orderNumber = orderNumber;
        this.userId = userId;
        this.programId = programId;
        this.eventTime = new Date();
        this.eventId = UUID.randomUUID().toString();
    }

    // 具体事件类型
    public static class OrderCreatedEvent extends OrderEvent {
        private BigDecimal orderAmount;
        private List<OrderTicketUserCreateDto> ticketUsers;

        public OrderCreatedEvent(String orderNumber, Long userId, Long programId,
                               BigDecimal orderAmount, List<OrderTicketUserCreateDto> ticketUsers) {
            super(orderNumber, userId, programId);
            this.orderAmount = orderAmount;
            this.ticketUsers = ticketUsers;
        }
    }

    public static class OrderPaidEvent extends OrderEvent {
        private String payTradeNo;
        private BigDecimal payAmount;
        private Date payTime;

        public OrderPaidEvent(String orderNumber, Long userId, Long programId,
                            String payTradeNo, BigDecimal payAmount, Date payTime) {
            super(orderNumber, userId, programId);
            this.payTradeNo = payTradeNo;
            this.payAmount = payAmount;
            this.payTime = payTime;
        }
    }

    public static class OrderCancelledEvent extends OrderEvent {
        private String cancelReason;
        private Date cancelTime;

        public OrderCancelledEvent(String orderNumber, Long userId, Long programId,
                                 String cancelReason, Date cancelTime) {
            super(orderNumber, userId, programId);
            this.cancelReason = cancelReason;
            this.cancelTime = cancelTime;
        }
    }
}
```

**EventListener事件监听器：**

```java
@Component
@Slf4j
public class OrderEventListener {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private RecommendationService recommendationService;

    /**
     * 监听订单创建事件
     */
    @EventListener
    @Async("businessThreadPool")
    public void handleOrderCreated(OrderEvent.OrderCreatedEvent event) {
        try {
            log.info("处理订单创建事件 orderNumber : {}", event.getOrderNumber());

            // 1. 发送订单确认通知
            notificationService.sendOrderConfirmation(event);

            // 2. 更新用户统计信息
            statisticsService.updateUserOrderStats(event.getUserId(), event.getOrderAmount());

            // 3. 更新节目统计信息
            statisticsService.updateProgramOrderStats(event.getProgramId(), event.getTicketUsers().size());

            // 4. 触发推荐算法更新
            recommendationService.updateUserPreference(event.getUserId(), event.getProgramId());

        } catch (Exception e) {
            log.error("处理订单创建事件失败 orderNumber : {}", event.getOrderNumber(), e);
        }
    }

    /**
     * 监听订单支付事件
     */
    @EventListener
    @Async("businessThreadPool")
    public void handleOrderPaid(OrderEvent.OrderPaidEvent event) {
        try {
            log.info("处理订单支付事件 orderNumber : {}", event.getOrderNumber());

            // 1. 发送支付成功通知
            notificationService.sendPaymentSuccess(event);

            // 2. 更新财务统计
            statisticsService.updateFinancialStats(event.getPayAmount(), event.getPayTime());

            // 3. 生成电子票
            ticketService.generateElectronicTicket(event.getOrderNumber());

            // 4. 更新用户积分
            pointService.addPointsForOrder(event.getUserId(), event.getPayAmount());

        } catch (Exception e) {
            log.error("处理订单支付事件失败 orderNumber : {}", event.getOrderNumber(), e);
        }
    }

    /**
     * 监听订单取消事件
     */
    @EventListener
    @Async("businessThreadPool")
    public void handleOrderCancelled(OrderEvent.OrderCancelledEvent event) {
        try {
            log.info("处理订单取消事件 orderNumber : {}", event.getOrderNumber());

            // 1. 发送取消通知
            notificationService.sendOrderCancellation(event);

            // 2. 更新统计信息
            statisticsService.updateCancellationStats(event.getProgramId(), event.getCancelReason());

            // 3. 分析取消原因
            analyticsService.analyzeCancellationReason(event);

        } catch (Exception e) {
            log.error("处理订单取消事件失败 orderNumber : {}", event.getOrderNumber(), e);
        }
    }
}
```

### 8.4 模板方法模式在订单处理中的应用

**模板方法模式架构：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            模板方法模式架构                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                        AbstractOrderProcessor                                  │
│                      ┌─────────────────────────┐                               │
│                      │ + processOrder()        │ ← Template Method             │
│                      │ # validateOrder()       │ ← Abstract Method             │
│                      │ # executeOrder()        │ ← Abstract Method             │
│                      │ # postProcess()         │ ← Hook Method                 │
│                      └─────────────────────────┘                               │
│                                    △                                            │
│                                    │                                            │
│                    ┌───────────────┼───────────────┐                           │
│                    │               │               │                           │
│         ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐           │
│         │SyncOrder        │ │AsyncOrder       │ │BatchOrder       │           │
│         │Processor        │ │Processor        │ │Processor        │           │
│         └─────────────────┘ └─────────────────┘ └─────────────────┘           │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**AbstractOrderProcessor抽象订单处理器：**

```java
public abstract class AbstractOrderProcessor {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 模板方法：定义订单处理流程
     */
    public final OrderProcessResult processOrder(OrderProcessRequest request) {
        OrderProcessResult result = new OrderProcessResult();
        result.setStartTime(new Date());

        try {
            // 1. 前置校验
            preValidate(request);

            // 2. 业务校验
            validateOrder(request);

            // 3. 执行订单处理
            executeOrder(request, result);

            // 4. 后置处理
            postProcess(request, result);

            result.setSuccess(true);
            result.setEndTime(new Date());

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(new Date());

            // 异常处理
            handleException(request, result, e);

            throw e;
        }

        return result;
    }

    /**
     * 前置校验（通用逻辑）
     */
    protected void preValidate(OrderProcessRequest request) {
        if (Objects.isNull(request)) {
            throw new IllegalArgumentException("订单处理请求不能为空");
        }

        if (Objects.isNull(request.getOrderData())) {
            throw new IllegalArgumentException("订单数据不能为空");
        }

        log.info("前置校验通过 requestId : {}", request.getRequestId());
    }

    /**
     * 业务校验（子类实现）
     */
    protected abstract void validateOrder(OrderProcessRequest request);

    /**
     * 执行订单处理（子类实现）
     */
    protected abstract void executeOrder(OrderProcessRequest request, OrderProcessResult result);

    /**
     * 后置处理（钩子方法，子类可选择性重写）
     */
    protected void postProcess(OrderProcessRequest request, OrderProcessResult result) {
        log.info("订单处理完成 requestId : {} duration : {}ms",
                request.getRequestId(),
                result.getEndTime().getTime() - result.getStartTime().getTime());
    }

    /**
     * 异常处理（钩子方法）
     */
    protected void handleException(OrderProcessRequest request, OrderProcessResult result, Exception e) {
        log.error("订单处理异常 requestId : {}", request.getRequestId(), e);

        // 可以在这里实现通用的异常处理逻辑
        // 如发送告警、记录错误日志等
    }

    /**
     * 获取处理器类型
     */
    public abstract String getProcessorType();

    /**
     * 获取处理器描述
     */
    public abstract String getProcessorDescription();
}
```

### 8.5 责任链模式在数据处理中的应用

**责任链模式架构：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            责任链模式架构                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  Request ──▶ Handler1 ──▶ Handler2 ──▶ Handler3 ──▶ Handler4 ──▶ Response     │
│              │            │            │            │                          │
│              ▼            ▼            ▼            ▼                          │
│         ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐                    │
│         │Data     │  │Data     │  │Data     │  │Data     │                    │
│         │Validate │  │Transform│  │Enrich   │  │Persist  │                    │
│         │Handler  │  │Handler  │  │Handler  │  │Handler  │                    │
│         └─────────┘  └─────────┘  └─────────┘  └─────────┘                    │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**AbstractDataHandler抽象数据处理器：**

```java
public abstract class AbstractDataHandler<T> {

    protected AbstractDataHandler<T> nextHandler;

    /**
     * 设置下一个处理器
     */
    public AbstractDataHandler<T> setNext(AbstractDataHandler<T> nextHandler) {
        this.nextHandler = nextHandler;
        return nextHandler;
    }

    /**
     * 处理请求
     */
    public final void handle(DataProcessContext<T> context) {
        try {
            // 执行当前处理器逻辑
            if (shouldHandle(context)) {
                doHandle(context);
            }

            // 传递给下一个处理器
            if (nextHandler != null) {
                nextHandler.handle(context);
            }

        } catch (Exception e) {
            handleException(context, e);
            throw e;
        }
    }

    /**
     * 判断是否应该处理
     */
    protected boolean shouldHandle(DataProcessContext<T> context) {
        return true;
    }

    /**
     * 具体处理逻辑
     */
    protected abstract void doHandle(DataProcessContext<T> context);

    /**
     * 异常处理
     */
    protected void handleException(DataProcessContext<T> context, Exception e) {
        log.error("数据处理异常 handler : {} data : {}",
                this.getClass().getSimpleName(), context.getData(), e);
    }

    /**
     * 获取处理器名称
     */
    public abstract String getHandlerName();
}
```

## 9. 性能优化与监控

### 9.1 缓存优化策略深度解析

**多级缓存架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              多级缓存架构                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │Browser      │───▶│CDN          │───▶│Nginx        │───▶│Application  │      │
│  │Cache        │    │Cache        │    │Cache        │    │Server       │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │客户端缓存   │    │边缘缓存     │    │反向代理缓存 │    │应用层缓存   │      │
│  │304响应      │    │静态资源     │    │页面缓存     │    │本地+Redis   │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**缓存策略核心要点：**

1. **缓存预热机制**
```java
@PostConstruct
public void warmUpCache() {
    // 预热热门节目数据
    List<Long> hotProgramIds = getHotProgramIds();
    hotProgramIds.parallelStream().forEach(programId -> {
        programService.getDetailWithCache(programId);
    });
}
```

2. **缓存更新策略**
```java
// 写入时更新缓存
@CacheEvict(value = "program", key = "#programId")
public void updateProgram(Long programId, ProgramUpdateDto updateDto) {
    // 更新数据库
    programMapper.updateById(updateDto);
    // 缓存会被自动清除，下次访问时重新加载
}
```

3. **缓存雪崩防护**
```java
// 添加随机过期时间
long randomTtl = baseTtl + ThreadLocalRandom.current().nextLong(300);
redisCache.set(key, value, randomTtl, TimeUnit.SECONDS);
```

### 9.2 数据库优化策略

**分库分表架构：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              分库分表架构                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                            ShardingSphere                                      │
│                      ┌─────────────────────────┐                               │
│                      │ Sharding-JDBC           │                               │
│                      │ 分库分表中间件          │                               │
│                      └─────────────────────────┘                               │
│                                    │                                            │
│                    ┌───────────────┼───────────────┐                           │
│                    │               │               │                           │
│         ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐           │
│         │DB_ORDER_0       │ │DB_ORDER_1       │ │DB_ORDER_2       │           │
│         │├─order_0        │ │├─order_0        │ │├─order_0        │           │
│         │├─order_1        │ │├─order_1        │ │├─order_1        │           │
│         │└─order_ticket_0 │ │└─order_ticket_0 │ │└─order_ticket_0 │           │
│         └─────────────────┘ └─────────────────┘ └─────────────────┘           │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**分片策略配置：**
```yaml
sharding:
  tables:
    t_order:
      actual-data-nodes: db_order_${0..2}.t_order_${0..15}
      database-strategy:
        inline:
          sharding-column: user_id
          algorithm-expression: db_order_${user_id % 3}
      table-strategy:
        inline:
          sharding-column: order_number
          algorithm-expression: t_order_${order_number % 16}
```

### 9.3 JVM优化配置

**生产环境JVM参数：**
```bash
-Xms4g -Xmx4g                           # 堆内存设置
-XX:NewRatio=1                          # 新生代与老年代比例
-XX:SurvivorRatio=8                     # Eden与Survivor比例
-XX:+UseG1GC                            # 使用G1垃圾收集器
-XX:MaxGCPauseMillis=200                # 最大GC暂停时间
-XX:+PrintGCDetails                     # 打印GC详情
-XX:+HeapDumpOnOutOfMemoryError         # OOM时生成堆转储
```

### 9.4 监控体系架构

**监控架构图：**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              监控体系架构                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │Application  │───▶│Micrometer   │───▶│Prometheus   │───▶│Grafana      │      │
│  │Metrics      │    │Metrics      │    │Time Series  │    │Dashboard    │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │业务指标     │    │指标收集     │    │时序存储     │    │可视化展示   │      │
│  │系统指标     │    │标准化输出   │    │数据聚合     │    │告警通知     │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**核心监控指标：**

1. **业务指标监控**
```java
@Component
public class BusinessMetrics {

    private final Counter orderCreatedCounter;
    private final Timer orderProcessTimer;
    private final Gauge activeOrderGauge;

    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.orderCreatedCounter = Counter.builder("order.created.total")
                .description("订单创建总数")
                .register(meterRegistry);

        this.orderProcessTimer = Timer.builder("order.process.duration")
                .description("订单处理耗时")
                .register(meterRegistry);
    }

    public void recordOrderCreated() {
        orderCreatedCounter.increment();
    }

    public void recordOrderProcessTime(Duration duration) {
        orderProcessTimer.record(duration);
    }
}
```

2. **系统指标监控**
```java
@Component
public class SystemMetrics {

    @EventListener
    public void recordJvmMetrics(ApplicationReadyEvent event) {
        // JVM指标
        new JvmMemoryMetrics().bindTo(meterRegistry);
        new JvmGcMetrics().bindTo(meterRegistry);
        new JvmThreadMetrics().bindTo(meterRegistry);

        // 系统指标
        new ProcessorMetrics().bindTo(meterRegistry);
        new UptimeMetrics().bindTo(meterRegistry);
    }
}
```

### 9.5 告警机制设计

**告警规则配置：**
```yaml
alerting:
  rules:
    - name: "高错误率告警"
      condition: "error_rate > 0.05"
      duration: "5m"
      severity: "critical"

    - name: "响应时间告警"
      condition: "response_time_p99 > 2000"
      duration: "3m"
      severity: "warning"

    - name: "订单处理积压告警"
      condition: "pending_orders > 1000"
      duration: "1m"
      severity: "critical"
```

**告警通知实现：**
```java
@Component
public class AlertNotificationService {

    public void sendAlert(AlertMessage alert) {
        switch (alert.getSeverity()) {
            case CRITICAL:
                // 发送短信 + 邮件 + 钉钉
                sendSms(alert);
                sendEmail(alert);
                sendDingTalk(alert);
                break;
            case WARNING:
                // 发送邮件 + 钉钉
                sendEmail(alert);
                sendDingTalk(alert);
                break;
            default:
                // 仅记录日志
                log.warn("告警信息: {}", alert);
        }
    }
}
```

## 总结

### 系统核心亮点

1. **高并发架构设计**
   - 多版本购票策略，V4版本支持万级并发
   - 本地锁 + 异步处理，响应时间控制在100ms内
   - Redis + Kafka异步解耦，削峰填谷

2. **数据一致性保证**
   - Lua脚本保证Redis操作原子性
   - 延迟队列处理超时订单
   - 对账机制保证最终一致性

3. **自研框架组件**
   - 线程池框架：TraceId传递、异常处理
   - Redis框架：多级缓存、穿透保护
   - 延迟队列：精确延迟、高可用
   - 分布式锁：读写锁、公平锁支持

4. **设计模式应用**
   - 策略模式：购票版本管理
   - 组合模式：校验体系设计
   - 观察者模式：事件驱动架构
   - 模板方法：订单处理流程

5. **监控运维体系**
   - 全链路监控：业务指标 + 系统指标
   - 多级告警：短信、邮件、钉钉通知
   - 性能优化：JVM调优、缓存优化

### 技术选型优势

| 技术组件 | 选型原因 | 核心优势 |
|---------|----------|----------|
| Spring Cloud | 微服务架构 | 服务治理、配置管理 |
| Redis | 高性能缓存 | 内存存储、丰富数据结构 |
| Kafka | 消息队列 | 高吞吐、持久化 |
| Redisson | 分布式锁 | 多种锁类型、高可用 |
| MyBatis Plus | ORM框架 | 代码生成、分页插件 |

### 学习建议

对于Java初学者，建议按以下顺序学习：

1. **基础概念理解**：先理解微服务、缓存、消息队列等基本概念
2. **核心流程掌握**：重点学习购票V4流程，理解异步处理思想
3. **设计模式学习**：学习策略模式、组合模式等在项目中的应用
4. **框架组件研究**：深入研究自研框架的设计思想和实现原理
5. **性能优化实践**：学习缓存优化、数据库优化等性能调优技巧

这个大麦网购票系统是一个优秀的高并发系统设计案例，涵盖了分布式系统设计的各个方面，值得深入学习和实践。

## 10. V4版本完整流程总结

### 10.1 V4版本架构概览

V4版本是大麦网购票系统的最新版本，采用**本地锁 + 异步处理**的架构模式，相比前三个版本在性能和可扩展性方面有显著提升。

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            V4版本整体架构图                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │用户请求     │───▶│网关路由     │───▶│订单服务     │───▶│异步处理     │        │
│  │购票接口     │    │负载均衡     │    │V4策略       │    │Kafka队列    │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │参数校验     │    │用户认证     │    │本地锁机制   │    │消息消费     │        │
│  │组合检查     │    │权限验证     │    │票档级锁定   │    │订单创建     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │Redis操作    │    │座位锁定     │    │Kafka发送    │    │延迟取消     │        │
│  │Lua脚本      │    │库存扣减     │    │异步消息     │    │Redisson队列 │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 10.2 V4版本核心特性

**1. 本地锁机制**
- 使用 `ReentrantLock` 替代分布式锁
- 按票档ID进行细粒度锁定
- 避免网络开销，提升性能

**2. 异步订单创建**
- 通过Kafka消息队列异步处理
- 快速响应用户请求
- 提高系统吞吐量

**3. 座位状态实时管理**
- 下单时立即锁定座位
- 支付成功后立即更新为已售出
- 取消订单时立即释放座位

**4. 延迟取消机制**
- 使用Redisson延迟队列
- 自动取消超时未支付订单
- 释放锁定的座位资源

### 10.3 V4版本详细流程分析

#### 10.3.1 异步创建订单完整流程

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        V4版本异步创建订单流程图                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  【第一阶段：请求处理与校验】                                                    │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │HTTP请求     │───▶│网关拦截     │───▶│参数校验     │───▶│组合检查器   │        │
│  │POST /order  │    │JWT验证      │    │@Valid注解   │    │多层级校验   │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │用户信息     │    │权限校验     │    │业务规则     │    │库存检查     │        │
│  │userId获取   │    │RBAC验证     │    │购票限制     │    │座位可用性   │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
│  【第二阶段：本地锁定与Redis操作】                                               │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │本地锁获取   │───▶│Lua脚本执行  │───▶│座位锁定     │───▶│库存扣减     │        │
│  │票档级锁定   │    │原子性操作   │    │状态变更     │    │数量更新     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │Redis更新    │    │座位Hash     │    │库存Hash     │    │用户记录     │        │
│  │多Key操作    │    │seat_lock_   │    │ticket_remain│    │order_count  │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
│  【第三阶段：异步消息发送】                                                      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │订单号生成   │───▶│MQ消息构建   │───▶│Kafka发送    │───▶│快速响应     │        │
│  │UUID生成     │    │序列化数据   │    │异步投递     │    │返回订单号   │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 10.3.2 Kafka消息消费与订单创建流程

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        Kafka消息消费与订单创建流程                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  【消息消费阶段】                                                                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │Kafka监听    │───▶│消息反序列化 │───▶│延迟时间计算 │───▶│幂等性检查   │        │
│  │@KafkaListener│   │JSON解析     │    │时间戳对比   │    │Redis标记    │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │消息内容     │    │订单数据     │    │延迟监控     │    │重复处理     │        │
│  │OrderCreateMq│    │用户信息     │    │性能指标     │    │防护机制     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
│  【订单创建阶段】                                                                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │数据库事务   │───▶│订单表插入   │───▶│订单详情插入 │───▶│事务提交     │        │
│  │@Transactional│   │主订单记录   │    │票档明细     │    │数据持久化   │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │延迟取消     │    │Redisson队列 │    │定时任务     │    │自动取消     │        │
│  │任务创建     │    │延迟投递     │    │超时检查     │    │座位释放     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 10.3.3 订单取消流程详解

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            V4版本订单取消流程                                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  【自动取消流程】                                                                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │延迟队列     │───▶│超时检测     │───▶│订单状态检查 │───▶│取消处理     │        │
│  │Redisson     │    │定时触发     │    │未支付订单   │    │状态更新     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │座位释放     │    │Redis更新    │    │库存恢复     │    │用户记录     │        │
│  │锁定→未售出  │    │Lua脚本      │    │数量增加     │    │计数减少     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
│  【手动取消流程】                                                                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │用户请求     │───▶│权限校验     │───▶│订单验证     │───▶│取消处理     │        │
│  │取消接口     │    │用户身份     │    │状态检查     │    │同自动取消   │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 10.3.4 支付成功后处理流程

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        V4版本支付成功处理流程                                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  【支付回调处理】                                                                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │支付回调     │───▶│签名验证     │───▶│订单查询     │───▶│状态更新     │        │
│  │第三方通知   │    │安全校验     │    │数据库查询   │    │支付成功     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │座位状态     │    │Redis更新    │    │延迟队列     │    │通知发送     │        │
│  │锁定→已售出  │    │实时更新     │    │取消任务删除 │    │用户通知     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
│  【注意：V4版本特点】                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │ • 支付成功后立即更新座位状态，不使用延迟消息                                │ │
│  │ • 保证用户看到的座位状态是实时的                                            │ │
│  │ • 删除延迟取消任务，避免误取消已支付订单                                    │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 10.4 V4版本对账流程

#### 10.4.1 数据一致性检查

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            V4版本对账流程架构                                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  【定时对账任务】                                                                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │定时调度     │───▶│数据采集     │───▶│一致性比对   │───▶│差异报告     │        │
│  │@Scheduled   │    │多数据源     │    │算法比较     │    │异常记录     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│         │                 │                 │                 │                │
│         ▼                 ▼                 ▼                 ▼                │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │数据源对比   │    │MySQL数据    │    │Redis数据    │    │差异修复     │        │
│  │三方数据源   │    │订单表       │    │座位Hash     │    │自动修复     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
│  【对账维度】                                                                    │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │座位状态     │    │库存数量     │    │订单状态     │    │支付状态     │        │
│  │一致性检查   │    │数量对比     │    │状态同步     │    │金额核对     │        │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 10.4.2 对账实现细节

**1. 座位数据对账**
```java
// 座位状态一致性检查
public SeatDataComparison compareSeatData(Long programId, Long ticketCategoryId) {
    // 1. 获取数据库中的座位数据
    List<SeatVo> dbSeats = seatMapper.selectSeatsByProgramAndCategory(programId, ticketCategoryId);

    // 2. 获取Redis中的座位数据
    Map<String, String> redisNoSoldSeats = redisTemplate.opsForHash()
        .entries(RedisKeyManage.SEAT_NO_SOLD_HASH_RESOLUTION.getKey() + programId + "_" + ticketCategoryId);
    Map<String, String> redisLockSeats = redisTemplate.opsForHash()
        .entries(RedisKeyManage.SEAT_LOCK_HASH_RESOLUTION.getKey() + programId + "_" + ticketCategoryId);
    Map<String, String> redisSoldSeats = redisTemplate.opsForHash()
        .entries(RedisKeyManage.SEAT_SOLD_HASH_RESOLUTION.getKey() + programId + "_" + ticketCategoryId);

    // 3. 数据比对与差异分析
    return performDataComparison(dbSeats, redisNoSoldSeats, redisLockSeats, redisSoldSeats);
}
```

**2. 库存数量对账**
```java
// 库存数量一致性检查
public InventoryComparison compareInventoryData(Long programId, Long ticketCategoryId) {
    // 1. 数据库统计
    int dbAvailableCount = seatMapper.countAvailableSeats(programId, ticketCategoryId);
    int dbSoldCount = orderDetailMapper.countSoldTickets(programId, ticketCategoryId);

    // 2. Redis统计
    String redisKey = RedisKeyManage.TICKET_REMAIN_NUMBER_HASH_RESOLUTION.getKey() + programId + "_" + ticketCategoryId;
    Integer redisRemainCount = (Integer) redisTemplate.opsForHash().get(redisKey, "remainNumber");

    // 3. 差异分析
    return analyzeInventoryDifference(dbAvailableCount, dbSoldCount, redisRemainCount);
}
```

### 10.5 V4版本核心代码实现分析

#### 10.5.1 V4策略核心实现

```java
@Component
public class ProgramOrderV4ServiceImpl implements ProgramOrderStrategy {

    @Override
    public String createOrder(ProgramOrderCreateDto programOrderCreateDto) {
        // 1. 组合检查器执行
        compositeContainer.execute(CompositeCheckType.PROGRAM_ORDER_CHECK.getValue(), programOrderCreateDto);

        // 2. 本地锁获取（按票档ID排序避免死锁）
        List<Long> sortedTicketCategoryIds = programOrderCreateDto.getTicketCategoryList()
            .stream()
            .map(TicketCategoryOrderDto::getTicketCategoryId)
            .sorted()
            .collect(Collectors.toList());

        List<ReentrantLock> locks = new ArrayList<>();
        try {
            // 获取所有需要的锁
            for (Long ticketCategoryId : sortedTicketCategoryIds) {
                ReentrantLock lock = lockMap.computeIfAbsent(ticketCategoryId, k -> new ReentrantLock());
                if (!lock.tryLock(LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                    throw new DaMaiFrameException(BaseCode.LOCK_ACQUIRE_TIMEOUT);
                }
                locks.add(lock);
            }

            // 3. Redis操作（Lua脚本保证原子性）
            String orderNumber = executeRedisOperations(programOrderCreateDto);

            // 4. 发送Kafka消息
            sendOrderCreateMessage(programOrderCreateDto, orderNumber);

            return orderNumber;

        } finally {
            // 释放所有锁
            locks.forEach(ReentrantLock::unlock);
        }
    }

    private String executeRedisOperations(ProgramOrderCreateDto dto) {
        // Lua脚本执行座位锁定和库存扣减
        String luaScript = buildLuaScript();
        List<String> keys = buildRedisKeys(dto);
        List<String> args = buildRedisArgs(dto);

        Object result = redisTemplate.execute(new DefaultRedisScript<>(luaScript, Object.class), keys, args.toArray());

        if (!"SUCCESS".equals(result)) {
            throw new DaMaiFrameException(BaseCode.SEAT_LOCK_FAILED);
        }

        return generateOrderNumber();
    }
}
```

